import gurobipy as gp
from gurobipy import GRB
import numpy as np
import math

class RobustMultiDriverOptimizer:
    def __init__(self):
        # 基本参数设置
        self.commission_rate = 0.18  # θ
        self.travel_cost_per_unit = 0.2  # α - 每单位时间的旅行成本
        self.penalty_cost_per_unit = 1.0  # β - 每单位时间的延迟惩罚成本
        self.stp_interval = 10  # 每个STP间隔10分钟
        self.driver_speed = 1.0  # 司机速度为1单位/时间
        self.max_orders_per_driver = 4  # U - 每个司机最多承接订单数
        self.driver_capacity = 12  # U_vol - 每个司机的车辆容量
        self.num_drivers = 2  # 2名司机
        
        # 鲁棒优化参数
        self.theta_t = 0.3  # 旅行时间不确定性预算系数
        self.travel_time_deviation_ratio = 0.2  # 旅行时间最大偏差比例
        
        # 时间设置：11:00-12:00，每10分钟一个STP
        self.stps = [0, 10, 20, 30, 40, 50]  # 分钟
        self.time_slots = [(0, 30), (30, 60)]  # 两个时段
        
        # 餐厅位置（2个餐厅）
        self.restaurant_coords = {
            0: (2, 8),
            1: (8, 2)
        }
        
        # 司机起始位置
        self.driver_start_coords = {
            0: (5, 1),
            1: (6, 5)
        }
        
        # 初始化订单数据
        self.setup_orders_data()
        
        print("初始化多司机鲁棒配送优化器")
        print(f"时间窗口: 11:00-12:00，分为{len(self.time_slots)}个时段")
        print(f"STP设置: 每{self.stp_interval}分钟一个，共{len(self.stps)}个")
        print(f"司机数量: {self.num_drivers}")
        print(f"每个STP订单数: 3")
        
    def setup_orders_data(self):
        """设置所有订单的硬编码数据"""
        self.orders_data = {}
        
        # STP 0 (0分钟) 的订单
        self.orders_data[0] = {
            'restaurant_id': 0,
            'customer_coord': (1, 5),
            'value': 25,
            'est_delivery': 15,
            'stp': 0,
            'volume': 5
        }
        self.orders_data[1] = {
            'restaurant_id': 1,
            'customer_coord': (9, 4),
            'value': 30,
            'est_delivery': 12,
            'stp': 0,
            'volume': 6
        }
        self.orders_data[2] = {
            'restaurant_id': 0,
            'customer_coord': (3, 6),
            'value': 28,
            'est_delivery': 18,
            'stp': 0,
            'volume': 6
        }
        
        # STP 1 (10分钟) 的订单
        self.orders_data[3] = {
            'restaurant_id': 1,
            'customer_coord': (7, 5),
            'value': 32,
            'est_delivery': 14,
            'stp': 1,
            'volume': 7
        }
        self.orders_data[4] = {
            'restaurant_id': 0,
            'customer_coord': (2, 7),
            'value': 26,
            'est_delivery': 16,
            'stp': 1,
            'volume': 6
        }
        self.orders_data[5] = {
            'restaurant_id': 1,
            'customer_coord': (8, 6),
            'value': 35,
            'est_delivery': 13,
            'stp': 1,
            'volume': 7
        }
        
        # STP 2 (20分钟) 的订单
        self.orders_data[6] = {
            'restaurant_id': 0,
            'customer_coord': (4, 5),
            'value': 29,
            'est_delivery': 15,
            'stp': 2,
            'volume': 6
        }
        self.orders_data[7] = {
            'restaurant_id': 1,
            'customer_coord': (6, 7),
            'value': 33,
            'est_delivery': 17,
            'stp': 2,
            'volume': 7
        }
        self.orders_data[8] = {
            'restaurant_id': 0,
            'customer_coord': (1, 8),
            'value': 27,
            'est_delivery': 20,
            'stp': 2,
            'volume': 6
        }
        
        # STP 3 (30分钟) 的订单
        self.orders_data[9] = {
            'restaurant_id': 1,
            'customer_coord': (9, 7),
            'value': 31,
            'est_delivery': 16,
            'stp': 3,
            'volume': 7
        }
        self.orders_data[10] = {
            'restaurant_id': 0,
            'customer_coord': (3, 4),
            'value': 24,
            'est_delivery': 14,
            'stp': 3,
            'volume': 5
        }
        self.orders_data[11] = {
            'restaurant_id': 1,
            'customer_coord': (7, 8),
            'value': 36,
            'est_delivery': 18,
            'stp': 3,
            'volume': 8
        }
        
        # STP 4 (40分钟) 的订单
        self.orders_data[12] = {
            'restaurant_id': 0,
            'customer_coord': (2, 6),
            'value': 28,
            'est_delivery': 15,
            'stp': 4,
            'volume': 6
        }
        self.orders_data[13] = {
            'restaurant_id': 1,
            'customer_coord': (8, 5),
            'value': 34,
            'est_delivery': 13,
            'stp': 4,
            'volume': 7
        }
        self.orders_data[14] = {
            'restaurant_id': 0,
            'customer_coord': (4, 7),
            'value': 30,
            'est_delivery': 17,
            'stp': 4,
            'volume': 6
        }
        
        # STP 5 (50分钟) 的订单
        self.orders_data[15] = {
            'restaurant_id': 1,
            'customer_coord': (6, 8),
            'value': 32,
            'est_delivery': 16,
            'stp': 5,
            'volume': 7
        }
        self.orders_data[16] = {
            'restaurant_id': 0,
            'customer_coord': (1, 6),
            'value': 26,
            'est_delivery': 18,
            'stp': 5,
            'volume': 6
        }
        self.orders_data[17] = {
            'restaurant_id': 1,
            'customer_coord': (9, 6),
            'value': 37,
            'est_delivery': 14,
            'stp': 5,
            'volume': 8
        }
    
    def get_orders_for_stp(self, stp_index):
        """获取特定STP的订单"""
        return [oid for oid, data in self.orders_data.items() if data['stp'] == stp_index]
    
    def calculate_distance(self, coord1, coord2):
        """计算两点间的欧几里得距离"""
        return math.sqrt((coord1[0] - coord2[0])**2 + (coord1[1] - coord2[1])**2)
    
    def get_time_slot_for_stp(self, stp_index):
        """确定STP属于哪个时段"""
        stp_time = self.stps[stp_index]
        for slot_idx, (start, end) in enumerate(self.time_slots):
            if start <= stp_time < end:
                return slot_idx
        return len(self.time_slots) - 1
    
    def solve_stp_robust_optimization(self, stp_index, driver_states, price_slot1, price_slot2):
        """求解单个STP的鲁棒优化问题"""
        stp_time = self.stps[stp_index]
        order_ids = self.get_orders_for_stp(stp_index)
        
        if not order_ids:
            # 如果没有订单，司机原地等待
            new_states = {}
            for k in range(self.num_drivers):
                new_states[k] = {
                    'pos': driver_states[k]['pos'],
                    'time': max(driver_states[k]['time'], stp_time)
                }
            return 0, 0, 0, new_states
        
        print(f"\n求解STP {stp_index} (时间={stp_time}分钟) 的鲁棒优化，订单: {order_ids}")
        
        # 创建鲁棒优化模型
        model = gp.Model(f"robust_multi_driver_stp_{stp_index}")
        model.setParam('OutputFlag', 0)
        model.setParam('TimeLimit', 120)
        model.setParam('MIPGap', 0.001)
        
        # 构建节点集合
        m = len(order_ids)
        pickup_nodes = list(range(m))
        delivery_nodes = list(range(m, 2*m))
        driver_start_nodes = list(range(2*m, 2*m + self.num_drivers))
        tau_node = 2*m + self.num_drivers  # 虚拟终点
        all_nodes = pickup_nodes + delivery_nodes + driver_start_nodes + [tau_node]
        
        # 计算节点坐标
        node_coords = {}
        for i, order_id in enumerate(order_ids):
            restaurant_id = self.orders_data[order_id]['restaurant_id']
            node_coords[i] = self.restaurant_coords[restaurant_id]  # 取货节点
            node_coords[i + m] = self.orders_data[order_id]['customer_coord']  # 送货节点
        
        for k in range(self.num_drivers):
            node_coords[2*m + k] = driver_states[k]['pos']  # 司机起始位置
        
        # 虚拟终点位置无关紧要
        node_coords[tau_node] = (5.5, 5.5)
        
        # 计算名义旅行时间和偏差
        nominal_times = {}
        deviations = {}
        for i in all_nodes:
            for j in all_nodes:
                if i != j:
                    if j == tau_node:
                        nominal_times[i, j] = 0  # 到虚拟终点的时间为0
                        deviations[i, j] = 0
                    else:
                        dist = self.calculate_distance(node_coords[i], node_coords[j])
                        nominal_times[i, j] = dist / self.driver_speed
                        deviations[i, j] = nominal_times[i, j] * self.travel_time_deviation_ratio
                else:
                    nominal_times[i, j] = 0
                    deviations[i, j] = 0
        
        # 决策变量
        # 路径变量
        x = {}
        for k in range(self.num_drivers):
            for i in all_nodes:
                for j in all_nodes:
                    if i != j:
                        x[i, j, k] = model.addVar(vtype=GRB.BINARY, name=f"x_{i}_{j}_{k}")
        
        # 预先计算最大可能的不确定性预算
        Gamma_max = m * 2  # 每个订单最多产生2条弧（取货和送货）
        
        # 递推到达时间变量 A[i,k,g]
        A = {}
        for i in all_nodes:
            for k in range(self.num_drivers):
                for g in range(Gamma_max + 1):
                    A[i, k, g] = model.addVar(vtype=GRB.CONTINUOUS, lb=0, name=f"A_{i}_{k}_{g}")
        
        # -- (已修正) 移除固定的不确定性预算 (Gamma_fixed) --

        # -- (新增) 为每个司机动态计算不确定性预算 Gamma_k --
        num_arcs = {}
        gamma = {}
        is_gamma = {}
        for k in range(self.num_drivers):
            # 路径上的弧数 (不包括到虚拟终点的)
            num_arcs[k] = gp.quicksum(x[i, j, k] for i in all_nodes for j in all_nodes if i != j and j != tau_node)
            
            # Gamma_k = ceil(theta_t * num_arcs_k)
            gamma[k] = model.addVar(vtype=GRB.INTEGER, name=f"gamma_{k}")
            model.addConstr(gamma[k] >= self.theta_t * num_arcs[k], name=f"gamma_lower_{k}")
            model.addConstr(gamma[k] <= self.theta_t * num_arcs[k] + 0.999, name=f"gamma_upper_{k}")
            
            # 辅助变量，用于处理变量索引问题
            for g in range(Gamma_max + 1):
                is_gamma[k, g] = model.addVar(vtype=GRB.BINARY, name=f"is_gamma_{k}_{g}")
            model.addConstr(gp.quicksum(is_gamma[k, g] for g in range(Gamma_max + 1)) == 1, name=f"gamma_is_one_val_{k}")
            model.addConstr(gamma[k] == gp.quicksum(g * is_gamma[k, g] for g in range(Gamma_max + 1)), name=f"link_gamma_{k}")
        
        # 最坏情况完成时间变量（每个司机）
        worst_completion_time = {}
        for k in range(self.num_drivers):
            start_time_k = max(driver_states[k]['time'], stp_time)
            worst_completion_time[k] = model.addVar(vtype=GRB.CONTINUOUS, lb=start_time_k, name=f"worst_completion_{k}")
        
        # 延迟变量
        delay = {}
        for i, order_id in enumerate(order_ids):
            delay[order_id] = model.addVar(vtype=GRB.CONTINUOUS, lb=0, name=f"delay_{order_id}")
        
        # 辅助变量
        load = {}
        for i in all_nodes:
            for k in range(self.num_drivers):
                load[i, k] = model.addVar(vtype=GRB.CONTINUOUS, lb=0, name=f"load_{i}_{k}")

        h = {}
        for i in all_nodes:
            for k in range(self.num_drivers):
                h[i, k] = model.addVar(vtype=GRB.CONTINUOUS, lb=0, name=f"h_{i}_{k}")
        
        # -- (新增) 辅助变量，用于计算最坏情况到达时间 --
        WorstArrivalAtNode = {}
        WA_aux = {}
        for i in all_nodes:
            for k in range(self.num_drivers):
                WorstArrivalAtNode[i, k] = model.addVar(vtype=GRB.CONTINUOUS, lb=0, name=f"WorstArrival_{i}_{k}")
                # 线性化 is_gamma[k, g] * A[i, k, g]
                for g in range(Gamma_max + 1):
                    WA_aux[i, k, g] = model.addVar(vtype=GRB.CONTINUOUS, lb=0, name=f"WA_aux_{i}_{k}_{g}")
                    model.addConstr(WA_aux[i, k, g] <= A[i, k, g], name=f"wa_aux_ub1_{i}_{k}_{g}")
                    model.addConstr(WA_aux[i, k, g] <= 10000 * is_gamma[k, g], name=f"wa_aux_ub2_{i}_{k}_{g}") # M * is_gamma
                    model.addConstr(WA_aux[i, k, g] >= A[i, k, g] - 10000 * (1 - is_gamma[k, g]), name=f"wa_aux_lb_{i}_{k}_{g}")
                
                start_time_k = max(driver_states[k]['time'], stp_time)
                model.addConstr(WorstArrivalAtNode[i, k] == start_time_k + gp.quicksum(WA_aux[i, k, g] for g in range(Gamma_max + 1)),
                              name=f"calc_worst_arrival_{i}_{k}")


        # 目标函数
        # 旅行成本：基于每个司机的工作时间
        total_travel_cost = gp.quicksum((worst_completion_time[k] - max(driver_states[k]['time'], stp_time)) * self.travel_cost_per_unit 
                                      for k in range(self.num_drivers))
        
        # 延迟成本
        total_penalty_cost = gp.quicksum(delay[oid] * self.penalty_cost_per_unit for oid in order_ids)
        
        model.setObjective(total_travel_cost + total_penalty_cost, GRB.MINIMIZE)
        
        # 约束条件
        
        # 1. VRP基本约束
        # 每个取货节点必须被访问一次
        for i in pickup_nodes:
            model.addConstr(gp.quicksum(x[i, j, k] for k in range(self.num_drivers) 
                                      for j in all_nodes if j != i) == 1, name=f"visit_pickup_{i}")
        
        # 取货和送货由同一司机完成
        for i in pickup_nodes:
            for k in range(self.num_drivers):
                model.addConstr(gp.quicksum(x[i, j, k] for j in all_nodes if j != i) == 
                              gp.quicksum(x[i + m, j, k] for j in all_nodes if j != i + m), 
                              name=f"pickup_delivery_{i}_{k}")
        
        # 车辆路径约束：确保路径的有效性
        for k in range(self.num_drivers):
            start_node = 2*m + k
            # 1. 每个司机从自己的起始点出发，并最终到达虚拟终点
            sum_out_from_start = gp.quicksum(x[start_node, j, k] for j in all_nodes if j != start_node)
            sum_in_to_tau = gp.quicksum(x[i, tau_node, k] for i in all_nodes if i != tau_node)
            model.addConstr(sum_out_from_start == sum_in_to_tau, name=f"start_end_balance_{k}")
            model.addConstr(sum_out_from_start <= 1, name=f"max_one_start_{k}")

        # 2. 禁止任何弧进入起始节点或从虚拟终点出发
        for k_drive in range(self.num_drivers):
            # 禁止从虚拟终点出发
            for j in all_nodes:
                if j != tau_node:
                    model.addConstr(x[tau_node, j, k_drive] == 0, name=f"no_arc_from_tau_{j}_{k_drive}")
            # 禁止进入任何起始节点
            for k_start in range(self.num_drivers):
                start_node = 2*m + k_start
                for i in all_nodes:
                    if i != start_node:
                        model.addConstr(x[i, start_node, k_drive] == 0, name=f"no_arc_to_start_{i}_{start_node}_{k_drive}")
        
        # 流平衡约束
        for j in pickup_nodes + delivery_nodes:
            for k in range(self.num_drivers):
                model.addConstr(gp.quicksum(x[i, j, k] for i in all_nodes if i != j) == 
                              gp.quicksum(x[j, i, k] for i in all_nodes if i != j), 
                              name=f"flow_balance_{j}_{k}")
        
        # 2. 容量约束 (基于体积)
        order_volumes = {i: self.orders_data[order_ids[i]]['volume'] for i in range(m)}
        
        M = 10000 # 一个足够大的数
        for k in range(self.num_drivers):
            start_node = 2*m + k
            model.addConstr(load[start_node, k] == 0, name=f"initial_load_{k}")

        for i in all_nodes:
            for j in all_nodes:
                if i != j:
                    for k in range(self.num_drivers):
                        load_change = 0
                        if j in pickup_nodes:
                            load_change = order_volumes[j]
                        elif j in delivery_nodes:
                            load_change = -order_volumes[j - m]
                        
                        model.addConstr(load[j, k] >= load[i, k] + load_change - M * (1 - x[i, j, k]), 
                                      name=f"load_update_{i}_{j}_{k}")

        for i in all_nodes:
            for k in range(self.num_drivers):
                model.addConstr(load[i, k] <= self.driver_capacity, name=f"capacity_check_{i}_{k}")

        # (新增) 最大订单数约束
        for k in range(self.num_drivers):
            model.addConstr(h[2*m + k, k] == 0, name=f"initial_order_count_{k}")

        for i in all_nodes:
            for j in all_nodes:
                if i != j:
                    for k in range(self.num_drivers):
                        l_i = 1 if i in pickup_nodes else 0
                        model.addConstr(h[j, k] >= h[i, k] + l_i - M * (1 - x[i, j, k]), 
                                      name=f"order_count_{i}_{j}_{k}")
        
        for i in pickup_nodes:
            for k in range(self.num_drivers):
                model.addConstr(h[i, k] <= self.max_orders_per_driver, name=f"max_orders_{i}_{k}")
        
        # 3. 递推约束实现（基于原始Robust Gurobi 2.py的逻辑）
        # 起始条件
        for k in range(self.num_drivers):
            start_node = 2*m + k
            start_time = max(driver_states[k]['time'], stp_time)
            for g in range(Gamma_max + 1):
                model.addConstr(A[start_node, k, g] == 0, name=f"A_start_{k}_{g}")  # 相对时间从0开始
        
        # 递推关系
        for i in all_nodes:
            for j in all_nodes:
                if i != j:
                    for k in range(self.num_drivers):
                        for g in range(Gamma_max + 1):
                            # 情况1：该弧不发生偏差
                            term1 = A[i, k, g] + nominal_times[i, j]
                            model.addConstr(A[j, k, g] >= term1 - M * (1 - x[i, j, k]),
                                          name=f"A_prop_{i}_{j}_{k}_{g}_no_dev")
                            
                            # 情况2：该弧发生偏差（仅当g > 0时）
                            if g > 0:
                                term2 = A[i, k, g-1] + nominal_times[i, j] + deviations[i, j]
                                model.addConstr(A[j, k, g] >= term2 - M * (1 - x[i, j, k]),
                                              name=f"A_prop_{i}_{j}_{k}_{g}_with_dev")
        
        # 4. 确定最坏情况完成时间 (已修正)
        for k in range(self.num_drivers):
            start_time_k = max(driver_states[k]['time'], stp_time)
            # 如果司机没有任务，其完成时间等于开始时间
            is_active = gp.quicksum(x[2*m + k, j, k] for j in pickup_nodes)
            model.addConstr(worst_completion_time[k] >= start_time_k, name=f"wct_lower_bound_{k}")
            model.addConstr(worst_completion_time[k] <= start_time_k + M * is_active, name=f"wct_idle_time_{k}")
            
            # 最坏完成时间是到达虚拟终点tau的最坏时间
            is_route_to_tau = gp.quicksum(x[i, tau_node, k] for i in all_nodes if i != tau_node)
            model.addConstr(worst_completion_time[k] >= WorstArrivalAtNode[tau_node, k] - M * (1 - is_route_to_tau),
                           name=f"wct_from_tau_{k}")
        
        # 5. 延迟计算 (已修正)
        for i, order_id in enumerate(order_ids):
            delivery_node = i + m
            target_time = stp_time + self.orders_data[order_id]['est_delivery']
            
            # 延迟是相对于所有可能的司机 k
            for k in range(self.num_drivers):
                # 只有当司机k配送此订单时，此约束才生效
                is_delivered_by_k = gp.quicksum(x[j, delivery_node, k] for j in all_nodes if j != delivery_node)
                model.addConstr(delay[order_id] >= WorstArrivalAtNode[delivery_node, k] - target_time - M * (1 - is_delivered_by_k),
                              name=f"delay_{order_id}_{k}")
        
        # 6. 时序约束（取货在送货之前）
        # 使用名义时间确保时序
        arrival_nominal = {}
        for i in all_nodes:
            for k in range(self.num_drivers):
                arrival_nominal[i, k] = model.addVar(vtype=GRB.CONTINUOUS, lb=0, name=f"arrival_nom_{i}_{k}")
        
        # 设置起始时间
        for k in range(self.num_drivers):
            start_node = 2*m + k
            model.addConstr(arrival_nominal[start_node, k] == 0, name=f"start_time_nom_{k}")
        
        # 时间传播
        for i in all_nodes:
            for j in all_nodes:
                if i != j:
                    for k in range(self.num_drivers):
                        model.addConstr(arrival_nominal[j, k] >= arrival_nominal[i, k] + nominal_times[i, j] - M * (1 - x[i, j, k]),
                                      name=f"time_flow_nom_{i}_{j}_{k}")
        
        # 如果节点未被访问，其时间为0
        for i in pickup_nodes + delivery_nodes:
            for k in range(self.num_drivers):
                model.addConstr(arrival_nominal[i, k] <= M * gp.quicksum(x[j, i, k] for j in all_nodes if j != i),
                              name=f"time_zero_if_not_visited_{i}_{k}")
        
        # 确保取货在送货之前
        for i in pickup_nodes:
            for k in range(self.num_drivers):
                model.addConstr(arrival_nominal[i, k] <= arrival_nominal[i + m, k], 
                              name=f"pickup_before_delivery_{i}_{k}")
        
        # 禁止从送货节点直接到对应的取货节点
        for i in pickup_nodes:
            for k in range(self.num_drivers):
                model.addConstr(x[i + m, i, k] == 0, name=f"no_delivery_to_pickup_{i}_{k}")
        
        print("开始求解鲁棒优化模型...")
        model.optimize()
        
        if model.status == GRB.OPTIMAL or model.status == GRB.SUBOPTIMAL:
            total_robust_cost = model.objVal
            travel_cost = sum((worst_completion_time[k].x - max(driver_states[k]['time'], stp_time)) * self.travel_cost_per_unit 
                            for k in range(self.num_drivers) if worst_completion_time[k].x > max(driver_states[k]['time'], stp_time) + 1e-6)
            penalty_cost = sum(delay[oid].x * self.penalty_cost_per_unit for oid in order_ids)
            
            print(f"鲁棒优化求解成功")
            print(f"总鲁棒成本: {total_robust_cost:.2f}")
            print(f"旅行成本: {travel_cost:.2f}")
            print(f"延迟成本: {penalty_cost:.2f}")
            
            # 输出路径信息
            for k in range(self.num_drivers):
                print(f"\n司机{k}的路径:")
                driver_path = []
                visited_actual_nodes = False
                for i in all_nodes:
                    for j in all_nodes:
                        if i != j and x[i, j, k].x > 0.5:
                            driver_path.append((i, j))
                            if j != tau_node and j != 2*m + k:
                                visited_actual_nodes = True
                
                print(f"  路径弧: {driver_path}")
                print(f"  司机路径弧数: {num_arcs[k].getValue():.0f}")
                print(f"  动态不确定性预算: {gamma[k].x}")
                
                if visited_actual_nodes:
                    work_time = worst_completion_time[k].x - max(driver_states[k]['time'], stp_time)
                    print(f"  工作时间: {work_time:.2f}")
                    print(f"  最坏情况完成时间: {worst_completion_time[k].x:.2f}")
                else:
                    print(f"  未分配订单")
            
            # 输出订单延迟信息
            print("\n订单延迟情况:")
            for order_id in order_ids:
                if delay[order_id].x > 0.01:
                    print(f"  订单{order_id}: 延迟{delay[order_id].x:.2f}分钟")
            
            # 更新司机状态
            new_states = {}
            for k in range(self.num_drivers):
                # 找到司机访问的最后一个实际节点
                last_real_node = 2*m + k  # 默认是起始位置
                last_real_time = max(driver_states[k]['time'], stp_time)
                final_pos = driver_states[k]['pos']

                # 检查司机是否有配送任务
                has_delivery = any(hasattr(x[2*m + k, j, k], 'x') and x[2*m + k, j, k].x > 0.5 for j in pickup_nodes)

                if has_delivery:
                    # 最后一个节点的位置是其路径上所有节点的加权平均（仅用于位置更新）
                    # 更好的方法是找到最后一个访问的节点
                    current_node = 2*m+k
                    # 简单的路径追踪来找到最后一个节点
                    path_nodes = []
                    visited = set()
                    while True:
                        path_nodes.append(current_node)
                        visited.add(current_node)
                        found_next = False
                        for j_node in all_nodes:
                            if j_node != current_node and j_node not in visited and hasattr(x.get((current_node, j_node, k)), 'x') and x[current_node, j_node, k].x > 0.5:
                                current_node = j_node
                                found_next = True
                                break
                        if not found_next or current_node == tau_node:
                            break
                    
                    if path_nodes and path_nodes[-1] != 2*m+k:
                       last_real_node = path_nodes[-1]

                    final_pos = node_coords[last_real_node]
                    last_real_time = worst_completion_time[k].x
                
                new_states[k] = {
                    'pos': final_pos,
                    'time': last_real_time
                }
            
            return total_robust_cost, travel_cost, penalty_cost, new_states
        
        else:
            print(f"鲁棒优化求解失败，状态码: {model.status}")
            # 返回原状态
            new_states = {}
            for k in range(self.num_drivers):
                new_states[k] = {
                    'pos': driver_states[k]['pos'],
                    'time': max(driver_states[k]['time'], stp_time)
                }
            return float('inf'), 0, 0, new_states
    
    def analyze_pricing_scheme(self, price_slot1, price_slot2):
        """分析单个定价方案的鲁棒表现"""
        print(f"\n{'='*70}")
        print(f"分析鲁棒定价方案: (时段1价格=¥{price_slot1}, 时段2价格=¥{price_slot2})")
        print(f"{'='*70}")
        
        # 初始化司机状态
        driver_states = {}
        for k in range(self.num_drivers):
            driver_states[k] = {
                'pos': self.driver_start_coords[k],
                'time': 0.0
            }
        
        total_revenue = 0
        total_travel_cost = 0
        total_penalty_cost = 0
        total_robust_cost = 0
        
        # 逐个STP求解
        for stp_idx in range(len(self.stps)):
            print(f"\n--- STP {stp_idx} (时间={self.stps[stp_idx]}分钟) ---")
            
            # 获取该STP的订单
            order_ids = self.get_orders_for_stp(stp_idx)
            
            if order_ids:
                # 计算收入
                slot_idx = self.get_time_slot_for_stp(stp_idx)
                delivery_price = price_slot1 if slot_idx == 0 else price_slot2
                
                stp_order_value = sum(self.orders_data[oid]['value'] for oid in order_ids)
                stp_commission = stp_order_value * self.commission_rate
                stp_delivery_fee = len(order_ids) * delivery_price
                stp_revenue = stp_commission + stp_delivery_fee
                total_revenue += stp_revenue
                
                print(f"订单: {order_ids}")
                print(f"订单总价值: ¥{stp_order_value}")
                print(f"平台佣金: ¥{stp_commission:.2f}")
                print(f"配送费 ({len(order_ids)}×¥{delivery_price}): ¥{stp_delivery_fee}")
                print(f"STP收入: ¥{stp_revenue:.2f}")
            
            # 求解鲁棒优化
            robust_cost, travel_cost, penalty_cost, new_driver_states = \
                self.solve_stp_robust_optimization(stp_idx, driver_states, price_slot1, price_slot2)
            
            if robust_cost < float('inf'):
                total_travel_cost += travel_cost
                total_penalty_cost += penalty_cost
                total_robust_cost += robust_cost
                driver_states = new_driver_states
                
                print(f"\n司机最终状态:")
                for k in range(self.num_drivers):
                    print(f"  司机{k}: 位置{driver_states[k]['pos']}, 时间{driver_states[k]['time']:.2f}")
            else:
                print("求解失败，跳过该STP")
        
        # 计算总利润
        total_profit = total_revenue - total_travel_cost - total_penalty_cost
        
        print(f"\n{'='*70}")
        print(f"方案汇总:")
        print(f"  总收入: ¥{total_revenue:.2f}")
        print(f"  总旅行成本: ¥{total_travel_cost:.2f}")
        print(f"  总延迟成本: ¥{total_penalty_cost:.2f}")
        print(f"  总鲁棒成本(旅行+延迟): ¥{total_travel_cost + total_penalty_cost:.2f}")
        print(f"  总利润: ¥{total_profit:.2f}")
        
        return {
            'scheme': (price_slot1, price_slot2),
            'revenue': total_revenue,
            'travel_cost': total_travel_cost,
            'penalty_cost': total_penalty_cost,
            'total_cost': total_travel_cost + total_penalty_cost,
            'profit': total_profit
        }
    
    def run_complete_analysis(self):
        """运行所有定价方案的鲁棒分析"""
        print("="*80)
        print("多司机食品配送差异化时段定价与订单调度鲁棒优化分析")
        print("基于递推公式的鲁棒优化方法")
        print("="*80)
        
        # 所有定价方案
        pricing_schemes = [(3, 3), (3, 6), (6, 3), (6, 6)]
        results = []
        
        # 分析每个方案
        for scheme in pricing_schemes:
            try:
                result = self.analyze_pricing_scheme(scheme[0], scheme[1])
                results.append(result)
            except Exception as e:
                print(f"分析方案{scheme}时出错: {str(e)}")
                import traceback
                traceback.print_exc()
                continue
        
        # 汇总结果
        print(f"\n{'='*80}")
        print("所有定价方案鲁棒结果汇总对比")
        print(f"{'='*80}")
        print(f"{'方案':<12} {'收入':<10} {'旅行成本':<10} {'延迟成本':<10} {'总成本':<12} {'利润':<10} {'最优':<6}")
        print("-" * 85)
        
        if results:
            best_profit = max(result['profit'] for result in results)
            
            for result in results:
                scheme_str = f"({result['scheme'][0]}, {result['scheme'][1]})"
                is_best = "★" if abs(result['profit'] - best_profit) < 0.01 else ""
                
                print(f"{scheme_str:<12} {result['revenue']:<10.2f} "
                      f"{result['travel_cost']:<10.2f} {result['penalty_cost']:<10.2f} "
                      f"{result['total_cost']:<12.2f} {result['profit']:<10.2f} {is_best:<6}")
            
            # 找出最优方案
            best_result = max(results, key=lambda x: x['profit'])
            
            print(f"\n{'='*80}")
            print("最优鲁棒定价方案详细分析")
            print(f"{'='*80}")
            print(f"最优方案: (时段1=¥{best_result['scheme'][0]}, 时段2=¥{best_result['scheme'][1]})")
            print(f"最大鲁棒系统利润: ¥{best_result['profit']:.2f}")
            print(f"\n鲁棒优化特点:")
            print(f"  - 考虑旅行时间不确定性")
            print(f"  - 不确定性预算系数: {self.theta_t}")
            print(f"  - 旅行时间偏差比例: {self.travel_time_deviation_ratio}")
            print(f"  - 基于递推公式计算最坏情况")
            
            return results, best_result
        else:
            print("所有方案分析均失败！")
            return [], None

def main():
    """主函数"""
    print("多司机鲁棒优化模型启动")
    print("考虑旅行时间不确定性的食品配送系统")
    
    try:
        # 创建优化器
        optimizer = RobustMultiDriverOptimizer()
        
        # 运行完整分析
        results, best_result = optimizer.run_complete_analysis()
        
        if best_result:
            print(f"\n{'='*80}")
            print("鲁棒优化分析成功完成！")
            print(f"{'='*80}")
        else:
            print("分析失败，请检查模型设置")
        
        return results, best_result
        
    except Exception as e:
        print(f"程序执行过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()
        return None, None

if __name__ == "__main__":
    results, best_result = main()