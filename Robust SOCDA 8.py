import math
import copy
from typing import List, Dict, Tuple, Any, Set
import numpy as np
from itertools import permutations

# --- (新增) 客户选择和动态定价相关函数 (源自 Robust Gurobi 8.py) ---

def _calculate_delivery_fee(order_id, base_price, potential_orders_data, delivery_distances, k_dist, k_val):
    """根据订单距离和价值计算动态配送费"""
    distance = delivery_distances[order_id]
    value = potential_orders_data[order_id]['value']
    fee = base_price + k_dist * distance + k_val * value
    return fee

def _get_customer_choice(customer_type, preferences, available_choices):
    """根据偏好获取单个顾客的选择 (直接查找，无插值)"""
    customer_prefs = preferences[customer_type]
    
    best_choice = 'no_purchase'
    min_rank = customer_prefs.get('no_purchase', float('inf'))

    for choice in available_choices:
        rank = customer_prefs.get(choice, float('inf'))
        if rank < min_rank:
            min_rank = rank
            best_choice = choice
            
    return None if best_choice == 'no_purchase' else best_choice

def get_all_customer_choices(pricing_scheme, num_customers, customer_types, customer_preferences, stp_interval,
                           potential_orders_data, delivery_distances, k_dist, k_val):
    """获取所有顾客在给定价格下的选择, 并返回选择详情和收入"""
    price_slot1, price_slot2 = pricing_scheme
    
    stps = list(range(6))
    slot1_stps = [i for i, stp_time in enumerate(stps) if stp_time * stp_interval < 30]
    slot2_stps = [i for i, stp_time in enumerate(stps) if stp_time * stp_interval >= 30]

    available_choices = []
    available_choices.extend([(stp_idx, price_slot1) for stp_idx in slot1_stps])
    available_choices.extend([(stp_idx, price_slot2) for stp_idx in slot2_stps])

    choices = []
    orders_by_stp = {stp: [] for stp in stps}
    
    customer_type_mapping = {cid: type_id for type_id, cids in customer_types.items() for cid in cids}

    for cid in range(num_customers):
        ctype = customer_type_mapping[cid]
        choice = _get_customer_choice(ctype, customer_preferences, available_choices)
        choices.append(choice)
        if choice:
            stp_idx, _ = choice
            orders_by_stp[stp_idx].append(cid)

    return choices, orders_by_stp

# --- RG2 等价鲁棒成本计算函数 -------------------------------------------------
def compute_rg2_robust_cost(sequence, start_pos, start_time, slot,
                             restaurant_coords, customer_coords,
                             order_restaurants,
                             driver_speed, slot_duration,
                             theta_t, deviation_ratio,
                             travel_cost_per_unit, penalty_cost_per_unit,
                             est_delivery_duration, depot_coord,
                             orders_data, stp_interval, current_stp_time, # 新增参数
                             override_first_arc_time: float = None,
                             forced_gamma: int = None): # 新增
    """(第四次修正) 按照 Robust Gurobi 2.py 的动态规划递推公式精确实现单条路径的鲁棒成本，并考虑备餐时间。
    返回 (robust_travel_cost, robust_penalty_cost, total_cost, final_pos, final_time)"""
    if not sequence:
        return 0.0, 0.0, 0.0, start_pos, start_time

    # 1) 构建节点、坐标和弧
    nodes_in_sequence = ['start']
    coords_in_sequence = [start_pos]
    arc_deliveries: Dict[int, List[int]] = {}

    for node_str in sequence:
        if node_str.startswith("pickup_"):
            oid = int(node_str.split("_")[1])
            rest_id = orders_data[oid]['restaurant_id']
            nodes_in_sequence.append(node_str)
            coords_in_sequence.append(restaurant_coords[rest_id])
        elif node_str.startswith("delivery_"):
            oid = int(node_str.split("_")[1])
            nodes_in_sequence.append(node_str)
            coords_in_sequence.append(customer_coords[oid])
    
    arcs = []
    for i in range(len(coords_in_sequence) - 1):
        arcs.append((coords_in_sequence[i], coords_in_sequence[i+1]))
        if nodes_in_sequence[i+1].startswith("delivery_"):
            oid = int(nodes_in_sequence[i+1].split("_")[1])
            arc_deliveries.setdefault(i, []).append(oid)
            
    # 2) 计算名义时间与偏差
    nominal_times = []
    deviations = []
    
    is_overridden = False
    if override_first_arc_time is not None and arcs:
        is_overridden = True
        nominal_times.append(override_first_arc_time)
        deviations.append(override_first_arc_time * deviation_ratio)

    for i, (a, b) in enumerate(arcs):
        if is_overridden and i == 0:
            continue
        dist = math.hypot(a[0] - b[0], a[1] - b[1])
        t_nom = dist / driver_speed
        nominal_times.append(t_nom)
        deviations.append(t_nom * deviation_ratio)
    
    # 3) Gamma预算
    if forced_gamma is not None:
        gamma = forced_gamma
    else:
        gamma = math.ceil(theta_t * len(arcs)) if arcs else 0

    # 4) 动态规划: dp[i][g] = 到达节点i用掉g个预算的最坏到达时间。
    dp = [[-1.0] * (gamma + 1) for _ in range(len(arcs) + 1)]
    dp[0][0] = start_time

    for i in range(len(arcs)):
        t_nom, dev = nominal_times[i], deviations[i]
        
        node_type = nodes_in_sequence[i]
        food_ready_time = -1.0
        if node_type.startswith("pickup_"):
            oid = int(node_type.split("_")[1])
            order_stp = round(current_stp_time / stp_interval) if stp_interval > 0 else 0
            prep_time = orders_data[oid]['preparation_time']
            food_ready_time = order_stp * stp_interval + prep_time
            
        for g in range(gamma + 1):
            if dp[i][g] == -1.0:
                continue
            
            current_departure = max(dp[i][g], food_ready_time) if food_ready_time != -1.0 else dp[i][g]

            # No dev
            next_arrival_no_dev = current_departure + t_nom
            if dp[i+1][g] == -1.0 or next_arrival_no_dev > dp[i+1][g]:
                dp[i+1][g] = next_arrival_no_dev
            
            # With dev
            if g < gamma:
                next_arrival_with_dev = current_departure + t_nom + dev
                if dp[i+1][g+1] == -1.0 or next_arrival_with_dev > dp[i+1][g+1]:
                    dp[i+1][g+1] = next_arrival_with_dev

    # 5) 基于DP结果计算成本
    worst_finish_time = -1.0
    if dp and dp[-1]:
        try:
            worst_finish_time = max(val for val in dp[-1] if val != -1.0)
        except ValueError:
            worst_finish_time = start_time
    
    total_travel_time = worst_finish_time - start_time
    robust_travel_cost = total_travel_time * travel_cost_per_unit

    robust_penalty_cost = 0.0
    for i in range(len(arcs)):
        if i in arc_deliveries:
            worst_arrival_at_delivery_node = -1.0
            if dp[i+1]:
                try:
                    worst_arrival_at_delivery_node = max(val for val in dp[i+1] if val != -1.0)
                except ValueError:
                    worst_arrival_at_delivery_node = -1.0

            if worst_arrival_at_delivery_node != -1.0:
                for oid in arc_deliveries[i]:
                    target_time = current_stp_time + est_delivery_duration[oid]
                    delay = max(worst_arrival_at_delivery_node - target_time, 0)
                    robust_penalty_cost += delay * penalty_cost_per_unit
    
    total_cost = robust_travel_cost + robust_penalty_cost
    final_service_pos = coords_in_sequence[-1] if arcs else start_pos
    
    return robust_travel_cost, robust_penalty_cost, total_cost, final_service_pos, worst_finish_time
# ---------------------------------------------------------------------------

class RobustOrderGroup:
    """鲁棒订单组类，包含订单序列和相关信息"""
    def __init__(self, orders: List[int], sequence: List[int], starting_order: int):
        self.orders = orders
        self.sequence = sequence
        self.starting_order = starting_order
        self.virtual_driver_state = None
        self.estimated_cost = 0.0
        self.dispatch_cost = 0.0
        self.robust_cost = 0.0
        
    def __len__(self):
        return len(self.orders)
        
    def contains_order(self, order_id: int) -> bool:
        return order_id in self.orders
    
    def get_orders_set(self) -> Set[int]:
        return set(self.orders)

class RobustOGGM:
    """鲁棒订单组生成方法 (Robust Order Group Generation Method)"""
    
    def __init__(self, food_delivery_optimizer):
        self.optimizer = food_delivery_optimizer
        self.travel_cost_per_unit = food_delivery_optimizer.travel_cost_per_unit
        self.penalty_cost_per_unit = food_delivery_optimizer.penalty_cost_per_unit
        self.slot_duration = food_delivery_optimizer.slot_duration
        self.driver_speed = food_delivery_optimizer.driver_speed
        self.max_orders_per_driver = food_delivery_optimizer.max_orders_per_driver
        self.driver_capacity = food_delivery_optimizer.driver_capacity
        self.restaurant_coords = food_delivery_optimizer.restaurant_coords
        self.orders_data = food_delivery_optimizer.orders_data
        
        # 提取顾客坐标
        self.customer_coords = {oid: data['customer_coord'] for oid, data in self.orders_data.items()}
        # 提取预期送达时间
        self.estimated_delivery_duration = {oid: data['est_delivery'] for oid, data in self.orders_data.items()}

        self.theta_t = 0.3
        self.travel_time_deviation_ratio = 0.2
        self.delay_threshold = 1.0
        self.stp_interval = food_delivery_optimizer.stp_interval
        self.delta_for_virtual_driver = 1 # 对应论文OGGM步骤2中的δ

        # 新增: 代理Gamma
        max_arcs_per_route = self.max_orders_per_driver * 2
        self.gamma_proxy = math.ceil(self.theta_t * max_arcs_per_route)
        print(f"Robust OGGM (扩展案例) 初始化完成，使用代理Gamma: {self.gamma_proxy}")
    
    def _is_sequence_capacity_feasible(self, sequence: List[str]) -> bool:
        """检查给定序列是否在全程满足容量约束"""
        if not sequence:
            return True
        current_load = 0
        for node in sequence:
            if node.startswith("pickup_"):
                oid = int(node.split("_")[1])
                current_load += self.orders_data[oid]['volume']
                if current_load > self.driver_capacity:
                    return False
            elif node.startswith("delivery_"):
                oid = int(node.split("_")[1])
                current_load -= self.orders_data[oid]['volume']
        return True

    def calculate_distance(self, coord1, coord2):
        return math.sqrt((coord1[0] - coord2[0])**2 + (coord1[1] - coord2[1])**2)
    
    def calculate_nominal_travel_time(self, coord1, coord2):
        distance = self.calculate_distance(coord1, coord2)
        return distance / self.driver_speed
    
    def calculate_sequence_robust_cost_budget_constrained(self, sequence, start_pos, start_time, stp, override_first_arc_time: float = None) -> float:
        current_stp_time = stp * self.stp_interval
        # 在OGGM内部，使用代理Gamma进行成本评估
        return compute_rg2_robust_cost(
            sequence, start_pos, start_time, -1, # slot is not used, pass dummy
            self.restaurant_coords, self.customer_coords, None,
            self.driver_speed, self.slot_duration,
            self.theta_t, self.travel_time_deviation_ratio,
            self.travel_cost_per_unit, self.penalty_cost_per_unit,
            self.estimated_delivery_duration, self.optimizer.driver_start_coords[0],
            self.orders_data, self.stp_interval, current_stp_time,
            override_first_arc_time,
            forced_gamma=self.gamma_proxy # 使用代理Gamma
        )[2]
    
    def get_order_coordinates(self, order_id: int) -> Tuple[Tuple[float, float], Tuple[float, float]]:
        """获取订单的餐厅和顾客坐标"""
        restaurant_id = self.orders_data[order_id]['restaurant_id']
        restaurant_coord = self.restaurant_coords[restaurant_id]
        customer_coord = self.customer_coords[order_id]
        return restaurant_coord, customer_coord
    
    def calculate_single_order_robust_cost(self, order_id: int, driver_start: Tuple[float, float], 
                                         start_time: float, stp: int) -> float:
        sequence = [f"pickup_{order_id}", f"delivery_{order_id}"]
        # 注意: 此处计算单个订单成本时，不应使用override，因为它依赖于特定的真实司机位置
        return self.calculate_sequence_robust_cost_budget_constrained(
            sequence, driver_start, start_time, stp)
    
    def calculate_two_orders_combined_robust_cost(self, order1_id: int, order2_id: int, 
                                                driver_start: Tuple[float, float],
                                                start_time: float, stp: int) -> Tuple[float, float]:
        rest1_coord, cust1_coord = self.get_order_coordinates(order1_id)
        rest2_coord, cust2_coord = self.get_order_coordinates(order2_id)
        
        # 机制1：固定order1的餐厅为第一个访问节点
        sequences_order1_first = [
            [f"pickup_{order1_id}", f"pickup_{order2_id}", f"delivery_{order2_id}", f"delivery_{order1_id}"],
            [f"pickup_{order1_id}", f"pickup_{order2_id}", f"delivery_{order1_id}", f"delivery_{order2_id}"],
            [f"pickup_{order1_id}", f"delivery_{order1_id}", f"pickup_{order2_id}", f"delivery_{order2_id}"]
        ]
        
        costs_order1_first = [self.calculate_sequence_robust_cost_budget_constrained(seq, driver_start, start_time, stp) for seq in sequences_order1_first]
        cost_order1_first = min(costs_order1_first)
        
        # 机制2：固定order2的餐厅为第一个访问节点
        sequences_order2_first = [
            [f"pickup_{order2_id}", f"pickup_{order1_id}", f"delivery_{order1_id}", f"delivery_{order2_id}"],
            [f"pickup_{order2_id}", f"pickup_{order1_id}", f"delivery_{order2_id}", f"delivery_{order1_id}"],
            [f"pickup_{order2_id}", f"delivery_{order2_id}", f"pickup_{order1_id}", f"delivery_{order1_id}"]
        ]
        
        costs_order2_first = [self.calculate_sequence_robust_cost_budget_constrained(seq, driver_start, start_time, stp) for seq in sequences_order2_first]
        cost_order2_first = min(costs_order2_first)
        
        return cost_order1_first, cost_order2_first
    
    def calculate_order_robust_matching_degree(self, order1_id: int, order2_id: int, 
                                             virtual_driver_pos: Tuple[float, float],
                                             start_time: float, stp: int) -> Tuple[float, float]:
        cost1_separate = self.calculate_single_order_robust_cost(order1_id, virtual_driver_pos, start_time, stp)
        cost2_separate = self.calculate_single_order_robust_cost(order2_id, virtual_driver_pos, start_time, stp)
        total_separate_cost = cost1_separate + cost2_separate
        
        cost_order1_first, cost_order2_first = self.calculate_two_orders_combined_robust_cost(
            order1_id, order2_id, virtual_driver_pos, start_time, stp)
        
        fit_o1_o2 = total_separate_cost - cost_order1_first
        fit_o2_o1 = total_separate_cost - cost_order2_first
        
        return fit_o1_o2, fit_o2_o1
    
    def cheapest_insertion_single_order_robust(self, sequence: List[str], new_order_id: int, 
                                             current_start_pos: Tuple[float, float],
                                             current_start_time: float, stp: int,
                                             override_first_arc_time: float = None) -> Tuple[List[str], float]:
        if not sequence:
            new_sequence = [f"pickup_{new_order_id}", f"delivery_{new_order_id}"]
            if not self._is_sequence_capacity_feasible(new_sequence):
                return new_sequence, float('inf')
            new_cost = self.calculate_sequence_robust_cost_budget_constrained(
                new_sequence, current_start_pos, current_start_time, stp,
                override_first_arc_time=override_first_arc_time)
            return new_sequence, new_cost
        
        best_sequence = None
        best_cost = float('inf')
        
        pickup_node = f"pickup_{new_order_id}"
        delivery_node = f"delivery_{new_order_id}"
        
        for pickup_pos in range(len(sequence) + 1):
            for delivery_pos in range(pickup_pos + 1, len(sequence) + 2):
                temp_sequence = sequence[:]
                temp_sequence.insert(pickup_pos, pickup_node)
                temp_sequence.insert(delivery_pos, delivery_node)
                
                if not self._is_sequence_capacity_feasible(temp_sequence):
                    continue
                
                cost = self.calculate_sequence_robust_cost_budget_constrained(
                    temp_sequence, current_start_pos, current_start_time, stp,
                    override_first_arc_time=override_first_arc_time)
                
                if cost < best_cost:
                    best_cost = cost
                    best_sequence = temp_sequence
        
        if not best_sequence:
            return [], float('inf')

        base_cost = self.calculate_sequence_robust_cost_budget_constrained(
            sequence, current_start_pos, current_start_time, stp,
            override_first_arc_time=override_first_arc_time)
        
        return best_sequence, best_cost - base_cost
    
    def calculate_max_delay_in_sequence(self, sequence: List[str], start_time: float, 
                                        start_pos: Tuple[float, float], stp: int,
                                        override_first_arc_time: float = None) -> float:
        """(已修正) 计算给定序列在鲁棒场景下的最大订单延迟，并考虑备餐时间。
        """
        if not sequence:
            return 0.0

        current_stp_time = stp * self.stp_interval
        
        # --- 使用与主成本函数相同的DP逻辑来计算最坏到达时间 ---
        # 1) 构建节点和弧
        nodes_in_sequence = ['start']
        coords_in_sequence = [start_pos if start_pos else (0,0)] # 修正虚拟司机没有start_pos的问题
        arc_deliveries: Dict[int, List[int]] = {}

        for node_str in sequence:
            if node_str.startswith("pickup_"):
                oid = int(node_str.split("_")[1])
                rest_id = self.orders_data[oid]['restaurant_id']
                nodes_in_sequence.append(node_str)
                coords_in_sequence.append(self.restaurant_coords[rest_id])
            elif node_str.startswith("delivery_"):
                oid = int(node_str.split("_")[1])
                nodes_in_sequence.append(node_str)
                coords_in_sequence.append(self.customer_coords[oid])
        
        arcs = []
        for i in range(len(coords_in_sequence) - 1):
            arcs.append((coords_in_sequence[i], coords_in_sequence[i+1]))
            if nodes_in_sequence[i+1].startswith("delivery_"):
                oid = int(nodes_in_sequence[i+1].split("_")[1])
                arc_deliveries.setdefault(i, []).append(oid)
            
        # 2) 名义时间与偏差
        nominal_times, deviations = [], []
        is_overridden = False
        if override_first_arc_time is not None and arcs:
            is_overridden = True
            nominal_times.append(override_first_arc_time)
            deviations.append(override_first_arc_time * self.travel_time_deviation_ratio)

        for i, (a, b) in enumerate(arcs):
            if is_overridden and i == 0: continue
            dist = math.hypot(a[0] - b[0], a[1] - b[1])
            t_nom = dist / self.driver_speed
            nominal_times.append(t_nom)
            deviations.append(t_nom * self.travel_time_deviation_ratio)

        if not arcs: return 0.0
            
        # 3) DP计算
        gamma = math.ceil(self.theta_t * len(arcs))
        dp = [[-1.0] * (gamma + 1) for _ in range(len(arcs) + 1)]
        dp[0][0] = start_time
        
        for i in range(len(arcs)):
            t_nom, dev = nominal_times[i], deviations[i]
            
            node_type = nodes_in_sequence[i]
            food_ready_time = -1.0
            if node_type.startswith("pickup_"):
                oid = int(node_type.split("_")[1])
                # 修正：stp信息直接从函数参数stp获取
                order_stp = stp
                prep_time = self.orders_data[oid]['preparation_time']
                food_ready_time = order_stp * self.stp_interval + prep_time
            
            for g in range(gamma + 1):
                if dp[i][g] == -1.0: continue
                
                current_departure = max(dp[i][g], food_ready_time) if food_ready_time != -1.0 else dp[i][g]

                # No dev
                next_arrival_no_dev = current_departure + t_nom
                if dp[i+1][g] == -1.0 or next_arrival_no_dev > dp[i+1][g]:
                    dp[i+1][g] = next_arrival_no_dev
                
                # With dev
                if g < gamma:
                    next_arrival_with_dev = current_departure + t_nom + dev
                    if dp[i+1][g+1] == -1.0 or next_arrival_with_dev > dp[i+1][g+1]:
                        dp[i+1][g+1] = next_arrival_with_dev

        # 4) 计算最大延迟度
        max_delay_ratio = 0.0
        for i in range(len(arcs)):
            if i in arc_deliveries:
                worst_arrival = -1.0
                if dp[i+1]:
                    try:
                        worst_arrival = max(val for val in dp[i+1] if val != -1.0)
                    except ValueError: pass
                
                if worst_arrival != -1.0:
                    for oid in arc_deliveries[i]:
                        target_time = current_stp_time + self.estimated_delivery_duration[oid]
                        delay = max(worst_arrival - target_time, 0)
                        est_dur = self.estimated_delivery_duration[oid]
                        if est_dur > 0:
                            delay_ratio = delay / est_dur
                            if delay_ratio > max_delay_ratio:
                                max_delay_ratio = delay_ratio
        
        return max_delay_ratio

    def calculate_max_robust_delay_degree_in_sequence(self, sequence: List[str], start_time: float,
                                                    start_pos: Tuple[float, float]) -> float:
        # 使用start_time作为当前STP时间的近似
        _, _, _, final_pos, final_time = compute_rg2_robust_cost(
            sequence, start_pos, start_time, -1,
            self.restaurant_coords, self.customer_coords, None,
            self.driver_speed, self.slot_duration, self.theta_t, self.travel_time_deviation_ratio,
            self.travel_cost_per_unit, self.penalty_cost_per_unit,
            self.estimated_delivery_duration, self.optimizer.driver_start_coords[0],
            self.orders_data, self.stp_interval, start_time)
        
        max_delay_degree = 0.0
        # Re-calculate to get individual delays
        # This is a simplification; a full delay calculation is complex
        # For now, we rely on the total penalty from the cost function as a proxy
        return 0.0 # Simplified for now
    
    def generate_robust_order_groups_for_stp(self, orders_in_stp: List[int], stp: int,
                                            driver_states: Dict) -> List[RobustOrderGroup]:
        print(f"\n=== 开始为STP {stp}生成鲁棒订单组 ===")
        print(f"STP {stp}订单: {orders_in_stp}")

        if not orders_in_stp:
            return []
        
        # 预先缓存 pickup 坐标，避免重复计算
        pickup_coords_cache = {
            oid: self.restaurant_coords[self.orders_data[oid]['restaurant_id']]
            for oid in orders_in_stp
        }

        all_order_groups = []
        for starting_order in orders_in_stp:
            # ---- OGGM步骤2: 为当前起始订单 o_i 确定虚拟司机 k'_i ----
            pick_coord = pickup_coords_cache[starting_order]

            # 2a. 找到 δ 个最近的真实司机
            driver_distances = []
            for did, state in driver_states.items():
                dist = self.calculate_distance(state['pos'], pick_coord)
                driver_distances.append({'did': did, 'state': state, 'dist': dist})
            
            driver_distances.sort(key=lambda x: x['dist'])
            num_drivers_to_consider = min(self.delta_for_virtual_driver, len(driver_distances))
            closest_drivers = driver_distances[:num_drivers_to_consider]

            # 2b. 计算平均距离和平均时间 (严格遵循论文)
            avg_dist_to_pickup = sum(d['dist'] for d in closest_drivers) / len(closest_drivers) if closest_drivers else 0
            v_start_time = sum(d['state']['time'] for d in closest_drivers) / len(closest_drivers) if closest_drivers else list(driver_states.values())[0]['time']
            # v_start_time = min(d['state']['time'] for d in closest_drivers) if closest_drivers else list(driver_states.values())[0]['time']
            override_time = avg_dist_to_pickup / self.driver_speed

            # ---- OGGM步骤5-8: 迭代生成订单组 ----
            current_order_cluster = {starting_order}
            current_sequence = [f"pickup_{starting_order}", f"delivery_{starting_order}"]
            
            # 为起始订单本身创建的第一个订单组，成本计算需要使用override_time
            current_cost = self.calculate_sequence_robust_cost_budget_constrained(
                current_sequence, None, v_start_time, stp, override_first_arc_time=override_time
            )
            initial_rog = RobustOrderGroup([starting_order], current_sequence[:], starting_order)
            initial_rog.robust_cost = current_cost
            all_order_groups.append(initial_rog)
            
            remaining_orders = set(orders_in_stp) - {starting_order}
            
            while remaining_orders and len(current_order_cluster) < self.max_orders_per_driver:
                # --- 1. Calculate composite fit for all remaining orders ---
                order_matching_scores = []
                w_cost, w_spatial, w_temporal = 1.0, 0.5, 0.3 # 权重可调

                for order_id in remaining_orders:
                    # Cost saving calculation
                    seq_o = [f"pickup_{order_id}", f"delivery_{order_id}"]
                    cost_o = self.calculate_sequence_robust_cost_budget_constrained(
                        seq_o, None, v_start_time, stp, override_first_arc_time=override_time)
                    _, cost_increase = self.cheapest_insertion_single_order_robust(
                        current_sequence, order_id, None, v_start_time, stp,
                        override_first_arc_time=override_time)
                    cost_combined = current_cost + cost_increase
                    cost_saving = current_cost + cost_o - cost_combined

                    # Spatial and temporal bonus calculation
                    temp_cluster_orders = current_order_cluster | {order_id}
                    all_nodes_coords = [pickup_coords_cache[oid] for oid in temp_cluster_orders] + \
                                       [self.customer_coords[oid] for oid in temp_cluster_orders]
                    spatial_bonus = 1.0 / (1.0 + np.mean([self.calculate_distance(coord, np.mean(all_nodes_coords, axis=0)) for coord in all_nodes_coords])) if all_nodes_coords else 0
                    
                    target_times = [stp * self.stp_interval + self.estimated_delivery_duration[oid] for oid in temp_cluster_orders]
                    temporal_bonus = 1.0 / (1.0 + np.var(target_times)) if len(target_times) > 1 else 0
                        
                    fit = w_cost * cost_saving + w_spatial * spatial_bonus + w_temporal * temporal_bonus
                    order_matching_scores.append((order_id, fit))

                order_matching_scores.sort(key=lambda x: x[1], reverse=True)
                
                if not order_matching_scores or order_matching_scores[0][1] <= 0:
                    break
                
                # --- 2. Look-ahead Heuristic ---
                K_LOOKAHEAD = 3
                W_LOOKAHEAD = 0.5 # 向前看收益的折扣权重
                best_order_to_add = None
                best_new_sequence = None

                candidate_evaluations = []
                for order_id, initial_fit in order_matching_scores[:K_LOOKAHEAD]:
                    temp_sequence, cost_increase = self.cheapest_insertion_single_order_robust(
                        current_sequence, order_id, None, v_start_time, stp,
                        override_first_arc_time=override_time)
                    
                    if not temp_sequence or cost_increase == float('inf'):
                        continue

                    max_delay_ratio = self.calculate_max_delay_in_sequence(
                        temp_sequence, v_start_time, None, stp, 
                        override_first_arc_time=override_time)
                        
                    if max_delay_ratio > self.delay_threshold:
                        continue
                        
                    cost_after_insertion = current_cost + cost_increase
                    lookahead_remaining = remaining_orders - {order_id}
                    max_next_fit = 0.0
                    if lookahead_remaining:
                        next_fits = []
                        for next_order_id in lookahead_remaining:
                            cost_next = self.calculate_sequence_robust_cost_budget_constrained(
                                [f"pickup_{next_order_id}", f"delivery_{next_order_id}"], None, v_start_time, stp, override_first_arc_time=override_time)
                            
                            _, cost_increase_next = self.cheapest_insertion_single_order_robust(
                                temp_sequence, next_order_id, None, v_start_time, stp,
                                override_first_arc_time=override_time)
                            cost_combined_next = cost_after_insertion + cost_increase_next
                            next_fit_saving = cost_after_insertion + cost_next - cost_combined_next
                            next_fits.append(next_fit_saving)
                        if next_fits:
                           max_next_fit = max(0, max(next_fits))
                           
                    combined_score = initial_fit + W_LOOKAHEAD * max_next_fit
                    candidate_evaluations.append({'order_id': order_id, 'score': combined_score, 'sequence': temp_sequence})
                
                if candidate_evaluations:
                    best_candidate = max(candidate_evaluations, key=lambda x: x['score'])
                    best_order_to_add = best_candidate['order_id']
                    best_new_sequence = best_candidate['sequence']
                else:
                    for order_id, fit in order_matching_scores[K_LOOKAHEAD:]:
                        temp_sequence, _ = self.cheapest_insertion_single_order_robust(
                            current_sequence, order_id, None, v_start_time, stp,
                            override_first_arc_time=override_time)
                        max_delay_ratio = self.calculate_max_delay_in_sequence(
                            temp_sequence, v_start_time, None, stp, 
                            override_first_arc_time=override_time)
                        if max_delay_ratio <= self.delay_threshold:
                            best_order_to_add = order_id
                            best_new_sequence = temp_sequence
                            break
                            
                # --- 3. Update state with the chosen order ---
                if best_order_to_add is None:
                    break

                # ---- OGGM步骤7-8: 确认合并 ----
                current_order_cluster.add(best_order_to_add)
                current_sequence = best_new_sequence
                remaining_orders.remove(best_order_to_add)
                
                current_cost = self.calculate_sequence_robust_cost_budget_constrained(
                    current_sequence, None, v_start_time, stp, override_first_arc_time=override_time
                )
                new_rog = RobustOrderGroup(list(current_order_cluster), current_sequence[:], starting_order)
                new_rog.robust_cost = current_cost
                all_order_groups.append(new_rog)
        
        print(f"STP {stp}生成{len(all_order_groups)}个订单组")
        unique_order_groups = []
        seen_order_sets = set()
        for rog in sorted(all_order_groups, key=lambda x: (len(x.orders), tuple(sorted(x.orders)))):
            orders_tuple = tuple(sorted(rog.orders))
            if orders_tuple not in seen_order_sets:
                unique_order_groups.append(rog)
                seen_order_sets.add(orders_tuple)
        
        print(f"去重后剩余 {len(unique_order_groups)} 个。")
        return unique_order_groups

class RobustOGSA:
    """鲁棒订单组选择算法 (Robust Order Group Selection Algorithm)"""
    
    def __init__(self, food_delivery_optimizer, robust_oggm):
        self.optimizer = food_delivery_optimizer
        self.robust_oggm = robust_oggm # 需要OGGM来重新计算成本
        self.travel_cost_per_unit = food_delivery_optimizer.travel_cost_per_unit
        self.penalty_cost_per_unit = food_delivery_optimizer.penalty_cost_per_unit
        self.slot_duration = food_delivery_optimizer.slot_duration
        self.driver_speed = food_delivery_optimizer.driver_speed
        self.max_orders_per_driver = food_delivery_optimizer.max_orders_per_driver
        self.driver_capacity = food_delivery_optimizer.driver_capacity
        self.orders_data = food_delivery_optimizer.orders_data
        self.restaurant_coords = food_delivery_optimizer.restaurant_coords
        self.customer_coords = {oid: data['customer_coord'] for oid, data in self.orders_data.items()}
        self.estimated_delivery_duration = {oid: data['est_delivery'] for oid, data in self.orders_data.items()}

        self.theta_t = 0.3
        self.travel_time_deviation_ratio = 0.2
        self.epsilon = 1.0
        self.delta_max = 3 # 对应论文OGSA和OGAH附录中描述的循环上限
        self.stp_interval = food_delivery_optimizer.stp_interval

        print("Robust OGSA (扩展案例) 初始化完成")
    
    def _is_sequence_capacity_feasible(self, sequence: List[str]) -> bool:
        """检查给定序列是否在全程满足容量约束"""
        if not sequence:
            return True
        current_load = 0
        for node in sequence:
            if node.startswith("pickup_"):
                oid = int(node.split("_")[1])
                current_load += self.orders_data[oid]['volume']
                if current_load > self.driver_capacity:
                    return False
            elif node.startswith("delivery_"):
                oid = int(node.split("_")[1])
                current_load -= self.orders_data[oid]['volume']
        return True

    def calculate_evaluation_function(self, dispatch_cost: float, num_orders: int) -> float:
        if num_orders <= 0: return float('inf')
        return dispatch_cost / (num_orders ** self.epsilon)
    
    def select_robust_order_groups_for_stp(self, order_groups: List[RobustOrderGroup], 
                                           all_orders: List[int], stp: int,
                                           driver_states: Dict) -> Dict[int, List[RobustOrderGroup]]:
        print(f"\n=== 开始Robust OGSA为STP {stp}选择订单组 ===")
        if not order_groups or not all_orders: return {}
        
        all_selections = {}

        # 附录K, 步骤2: 遍历 delta 从 1 到 delta_max
        for delta in range(1, self.delta_max + 1):
            print(f"\n--- OGSA: 评估视角 delta = {delta} ---")
            
            # 附录K, 步骤3-4: 为每个OG基于当前delta重新估算成本
            evaluated_ogs = []
            for og in order_groups:
                if not og.sequence: continue

                # 获取第一个访问节点坐标 (附录K, 步骤3)
                first_node_str = og.sequence[0]
                oid = int(first_node_str.split("_")[1])
                first_node_coord = self.restaurant_coords[self.orders_data[oid]['restaurant_id']]
                
                # 找到 delta 个最近的真实司机
                driver_distances = []
                for did, state in driver_states.items():
                    dist = math.hypot(state['pos'][0] - first_node_coord[0], state['pos'][1] - first_node_coord[1])
                    driver_distances.append({'state': state, 'dist': dist})
                
                driver_distances.sort(key=lambda x: x['dist'])
                
                num_drivers_for_avg = min(delta, len(driver_distances))
                if num_drivers_for_avg == 0: continue
                closest_drivers = driver_distances[:num_drivers_for_avg]

                # 计算虚拟司机状态 (附录K, 步骤3)
                avg_start_time = sum(d['state']['time'] for d in closest_drivers) / len(closest_drivers)
                avg_dist_to_pickup = sum(d['dist'] for d in closest_drivers) / len(closest_drivers)
                override_time = avg_dist_to_pickup / self.driver_speed

                # 使用 OGGM 的成本计算函数重新计算成本 (附录K, 步骤4)
                cost = self.robust_oggm.calculate_sequence_robust_cost_budget_constrained(
                    og.sequence, 
                    None, # start_pos is irrelevant due to override_time
                    avg_start_time, 
                    stp, 
                    override_first_arc_time=override_time
                )

                # 附录K, 步骤5: 计算评价值
                eval_val = self.calculate_evaluation_function(cost, len(og))
                evaluated_ogs.append({'og': og, 'cost': cost, 'eval': eval_val})

            # 贪心集合覆盖启发式
            remaining_ogs_to_cover = copy.deepcopy(evaluated_ogs)
            uncovered_orders = set(all_orders)
            current_selection = []

            while uncovered_orders and remaining_ogs_to_cover:
                # 筛选可以覆盖剩余订单的OG
                candidate_ogs = [e_og for e_og in remaining_ogs_to_cover if not e_og['og'].get_orders_set().isdisjoint(uncovered_orders)]
                
                if not candidate_ogs:
                    break
                
                # 附录K, 步骤6: 找到评价值最低的OG
                candidate_ogs.sort(key=lambda x: x['eval'])
                best_evaluated_og = candidate_ogs[0]
                best_og = best_evaluated_og['og']
                
                current_selection.append(best_og)
                
                # 附录K, 步骤7: 更新
                uncovered_orders -= best_og.get_orders_set()
                selected_orders_set = best_og.get_orders_set()
                remaining_ogs_to_cover = [e_og for e_og in remaining_ogs_to_cover if e_og['og'].get_orders_set().isdisjoint(selected_orders_set)]
            
            # 存储当前 delta 的方案
            if not uncovered_orders:
                all_selections[delta] = current_selection
                print(f"--- 视角 delta={delta}: 成功覆盖所有订单，选择了 {len(current_selection)} 个订单组 ---")
            else:
                all_selections[delta] = current_selection # 即使未完全覆盖也记录
                print(f"--- 视角 delta={delta}: 警告, 未能完全覆盖所有订单，还有 {len(uncovered_orders)} 个未覆盖 ---")

        return all_selections

class RobustDriver:
    """鲁棒司机状态类"""
    def __init__(self, driver_id: int, start_pos: Tuple[float, float], start_time: float, capacity: int):
        self.driver_id = driver_id
        self.initial_pos = start_pos
        self.initial_time = start_time
        self.current_pos = start_pos
        self.current_time = start_time
        self.remaining_capacity = capacity
        self.assigned_orders = []
        self.sequence = []
        self.total_robust_cost = 0.0
        
    def can_handle_order_group(self, order_group) -> bool:
        return self.remaining_capacity >= len(order_group)
    
    def assign_order_group(self, order_group, cost_details: Dict):
        """(已修改) 使用包含完整路径信息的字典来更新司机状态"""
        self.assigned_orders.extend(order_group.orders)
        self.remaining_capacity -= len(order_group)
        self.total_robust_cost = cost_details['total_cost']
        self.current_pos = cost_details['final_pos']
        self.current_time = cost_details['final_time']
        self.sequence = cost_details['final_sequence']

class RobustOGAH:
    """鲁棒订单组分配启发式算法 (Robust Order Group Assignment Heuristic)"""
    
    def __init__(self, food_delivery_optimizer):
        self.optimizer = food_delivery_optimizer
        self.travel_cost_per_unit = food_delivery_optimizer.travel_cost_per_unit
        self.penalty_cost_per_unit = food_delivery_optimizer.penalty_cost_per_unit
        self.slot_duration = food_delivery_optimizer.slot_duration
        self.driver_speed = food_delivery_optimizer.driver_speed
        self.max_orders_per_driver = food_delivery_optimizer.max_orders_per_driver
        self.driver_capacity = food_delivery_optimizer.driver_capacity
        self.orders_data = food_delivery_optimizer.orders_data
        self.restaurant_coords = food_delivery_optimizer.restaurant_coords
        self.customer_coords = {oid: data['customer_coord'] for oid, data in self.orders_data.items()}
        self.estimated_delivery_duration = {oid: data['est_delivery'] for oid, data in self.orders_data.items()}
        self.num_drivers = food_delivery_optimizer.num_drivers
        
        self.theta_t = 0.3
        self.travel_time_deviation_ratio = 0.2
        self.stp_interval = food_delivery_optimizer.stp_interval
        self.delta_max = 3 # OGAH附录中描述的循环上限

        print("Robust OGAH (扩展案例) 初始化完成")
    
    def _is_sequence_capacity_feasible(self, sequence: List[str]) -> bool:
        """检查给定序列是否在全程满足容量约束"""
        if not sequence:
            return True
        current_load = 0
        for node in sequence:
            if node.startswith("pickup_"):
                oid = int(node.split("_")[1])
                current_load += self.orders_data[oid]['volume']
                if current_load > self.driver_capacity:
                    return False
            elif node.startswith("delivery_"):
                oid = int(node.split("_")[1])
                current_load -= self.orders_data[oid]['volume']
        return True

    def calculate_robust_driver_dispatch_cost(self, order_group, driver: RobustDriver, stp: int):
        """(已修改) 计算分配一个订单组给司机的边际成本，并返回新路径的完整信息"""
        num_orders_in_og = len(order_group.orders)
        if driver.remaining_capacity < num_orders_in_og:
            return float('inf'), {}
        
        # 将新订单组的序列附加到司机现有序列后，形成新的完整路径
        new_sequence = driver.sequence + order_group.sequence
        
        # 检查新路径的容量可行性
        if not self._is_sequence_capacity_feasible(new_sequence):
            return float('inf'), {}
        
        # 基于司机的初始状态，计算新完整路径的总鲁棒成本
        # 此时 compute_rg2_robust_cost 内的 gamma 将基于完整的 new_sequence 计算
        current_stp_time = stp * self.stp_interval
        robust_travel, robust_penalty, new_total_cost, final_pos, final_time = compute_rg2_robust_cost(
            new_sequence, driver.initial_pos, driver.initial_time, -1,
            self.restaurant_coords, self.customer_coords, None,
            self.driver_speed, self.slot_duration, self.theta_t, self.travel_time_deviation_ratio,
            self.travel_cost_per_unit, self.penalty_cost_per_unit,
            self.estimated_delivery_duration, self.optimizer.driver_start_coords[0],
            self.orders_data, self.stp_interval, current_stp_time)

        # 成本是边际成本
        marginal_cost = new_total_cost - driver.total_robust_cost

        details = {
            'total_cost': new_total_cost,
            'robust_travel_cost': robust_travel,
            'robust_penalty_cost': robust_penalty,
            'final_pos': final_pos,
            'final_time': final_time,
            'final_sequence': new_sequence
        }
        return marginal_cost, details
    
    def calculate_regret_value(self, dispatch_costs: List[float]) -> float:
        if len(dispatch_costs) < 2: return 0.0
        costs = sorted(dispatch_costs)
        # 遵从论文公式(25)，它是一个2-regret，基于前3个最佳成本
        # sum_{j=1 to 3} (f_nx,j - f_nx,1) = (f_2 - f_1) + (f_3 - f_1)
        # range(1, min(3, len(costs))) -> i = 1, 2
        return sum(costs[i] - costs[0] for i in range(1, min(3, len(costs))))
    
    def initialize_robust_drivers(self, stp: int, initial_states: Dict = None) -> List[RobustDriver]:
        drivers = []
        stp_start_time = stp * self.stp_interval
        
        if initial_states:
            for driver_id, prev_state in initial_states.items():
                start_time = max(prev_state['time'], stp_start_time)
                driver = RobustDriver(driver_id, prev_state['pos'], start_time, self.max_orders_per_driver)
                drivers.append(driver)
        else:
            for driver_id in range(self.num_drivers):
                start_pos = self.optimizer.driver_start_coords[driver_id]
                driver = RobustDriver(driver_id, start_pos, stp_start_time, self.max_orders_per_driver)
                drivers.append(driver)
        return drivers
    
    def assign_robust_order_groups_to_drivers(self, selected_order_groups_by_driver: Dict[int, List[RobustOrderGroup]], stp: int, 
                                            initial_driver_states: Dict = None) -> Tuple[float, List[RobustDriver], Dict]:
        print(f"\n=== 开始Robust OGAH为STP {stp}分配订单组 ===")
        if not selected_order_groups_by_driver:
            final_states = []
            if initial_driver_states:
                for did, state in initial_driver_states.items():
                    driver = RobustDriver(did, state['pos'], max(state['time'], stp * self.optimizer.stp_interval), self.optimizer.max_orders_per_driver)
                    final_states.append(driver)
            return 0.0, final_states, {'assignments': [], 'total_robust_travel_cost': 0, 'total_robust_penalty_cost': 0}

        best_total_cost = float('inf')
        best_final_drivers = None
        best_assignment_details = {}

        # OGAH现在遍历由OGSA为每个delta视角生成的方案
        for delta_perspective, selected_groups in selected_order_groups_by_driver.items():
            print(f"\n--- OGAH: 评估基于视角 delta={delta_perspective} 的方案 ---")
            
            drivers = self.initialize_robust_drivers(stp, initial_driver_states)
            available_drivers = drivers[:]
            unassigned_groups = selected_groups[:]
            
            current_assignments = []
            
            while unassigned_groups and available_drivers:
                og_evaluations = []
                for og in unassigned_groups:
                    driver_costs, driver_details = [], []
                    for driver in available_drivers:
                        cost, details = self.calculate_robust_driver_dispatch_cost(og, driver, stp)
                        driver_costs.append(cost)
                        driver_details.append((driver, details))
                    
                    valid_assigns = [(c, d, dt) for c, (d, dt) in zip(driver_costs, driver_details) if c < float('inf')]
                    if valid_assigns:
                        valid_assigns.sort(key=lambda x: x[0])
                        regret = self.calculate_regret_value([c for c,_,_ in valid_assigns])
                        best_marginal_cost, best_driver, best_details = valid_assigns[0]
                        og_evaluations.append({
                            'og': og, 'regret': regret, 'best_marginal_cost': best_marginal_cost,
                            'best_driver': best_driver, 'best_details': best_details
                        })
                
                if not og_evaluations: break
                
                og_evaluations.sort(key=lambda x: x['regret'], reverse=True)
                best_assign = og_evaluations[0]
                
                sel_og = best_assign['og']
                sel_driver = best_assign['best_driver']
                sel_details = best_assign['best_details']
                
                sel_driver.assign_order_group(sel_og, sel_details)
                
                current_assignments.append({
                    'order_group': sel_og.orders, 'driver_id': sel_driver.driver_id, 
                    'marginal_cost': best_assign['best_marginal_cost'],
                    'new_total_cost': sel_details['total_cost']
                })
                
                unassigned_groups.remove(sel_og)

            if unassigned_groups:
                 print(f"警告: 方案(视角 delta={delta_perspective}) 中, {len(unassigned_groups)}个订单组未能分配")

            # 计算本次方案的总成本
            current_total_cost = sum(d.total_robust_cost for d in drivers)
            print(f"--- OGAH 方案(视角 delta={delta_perspective}) 结束, 总成本: {current_total_cost:.3f} ---")

            if current_total_cost < best_total_cost:
                best_total_cost = current_total_cost
                best_final_drivers = drivers
                
                final_travel = 0
                final_penalty = 0
                for driver in best_final_drivers:
                    if driver.sequence:
                         # 修正: 使用stp的固定开始时间作为延迟计算基准，而不是司机的实际开始时间
                         current_stp_time = stp * self.stp_interval
                         travel_cost, pen, _, _, _ = compute_rg2_robust_cost(
                            driver.sequence, driver.initial_pos, driver.initial_time, -1,
                            self.restaurant_coords, self.customer_coords, None, self.driver_speed, self.slot_duration,
                            self.theta_t, self.travel_time_deviation_ratio, self.travel_cost_per_unit, self.penalty_cost_per_unit,
                            self.estimated_delivery_duration, self.optimizer.driver_start_coords[0], self.orders_data, self.stp_interval, current_stp_time)
                         final_penalty += pen
                         final_travel += travel_cost

                best_assignment_details = {
                    'assignments': current_assignments,
                    'total_robust_travel_cost': final_travel,
                    'total_robust_penalty_cost': final_penalty
                }

        print(f"Robust OGAH完成，最优总成本: {best_total_cost:.3f}")
        return best_total_cost, best_final_drivers, best_assignment_details

class RobustSOCDA:
    """鲁棒单波次订单整合调度算法 (Robust Single Order Consolidation Dispatching Algorithm)"""
    
    def __init__(self, food_delivery_optimizer):
        self.optimizer = food_delivery_optimizer
        self.robust_oggm = RobustOGGM(food_delivery_optimizer)
        self.robust_ogsa = RobustOGSA(food_delivery_optimizer, self.robust_oggm)
        self.robust_ogah = RobustOGAH(food_delivery_optimizer)
        
        print("Robust SOCDA (扩展案例) 初始化完成")
    
    def solve_single_stp_robust_dispatch(self, orders_in_stp: List[int], stp: int, 
                                         initial_driver_states: Dict) -> Tuple[float, float, List[RobustDriver], Dict]:
        print(f"\n{'='*60}")
        print(f"Robust SOCDA求解STP {stp}鲁棒调度问题, 订单: {orders_in_stp}")
        
        if not orders_in_stp:
            final_drivers = []
            if initial_driver_states:
                for did, state in initial_driver_states.items():
                    driver = RobustDriver(did, state['pos'], max(state['time'], stp * self.optimizer.stp_interval), self.optimizer.max_orders_per_driver)
                    final_drivers.append(driver)
            return 0.0, 0.0, final_drivers, {'message': 'No orders'}
        
        # Step 1: OGGM
        robust_order_groups = self.robust_oggm.generate_robust_order_groups_for_stp(
            orders_in_stp, stp, initial_driver_states)
        if not robust_order_groups: 
            return 0.0, 0.0, self.robust_ogah.initialize_robust_drivers(stp, initial_driver_states), {}
            
        # Step 2: OGSA - 现在返回一个包含多个方案的字典
        # {delta_perspective: [selected_groups]}
        selected_groups_by_driver_perspective = self.robust_ogsa.select_robust_order_groups_for_stp(
            robust_order_groups, orders_in_stp, stp, initial_driver_states)
        if not selected_groups_by_driver_perspective: 
            return 0.0, 0.0, self.robust_ogah.initialize_robust_drivers(stp, initial_driver_states), {}

        # Step 3: OGAH - 现在接收多个方案并从中择优
        total_cost, final_drivers, details = self.robust_ogah.assign_robust_order_groups_to_drivers(
            selected_groups_by_driver_perspective, stp, initial_driver_states)
            
        travel_cost = details.get('total_robust_travel_cost', 0)
        penalty_cost = details.get('total_robust_penalty_cost', 0)

        print(f"\nSTP {stp} 求解完成: 总成本={total_cost:.2f}")
        return travel_cost, penalty_cost, final_drivers, details

def compute_exact_robust_cost_for_route(full_sequence, start_pos, start_time, stp, optimizer):
    """
    (源自 Robust Hybrid 4.py) 使用精确的"全局Gamma"DP逻辑，重新计算给定路径的旅行成本和延迟成本，并考虑等待时间。
    该方法模拟了精确求解器的底层成本计算逻辑，以解决成本计算不一致的问题。
    """
    if not full_sequence:
        return 0.0, 0.0, 0.0

    # 1. 提取参数与构建基础信息
    orders_data = optimizer.orders_data
    restaurant_coords = optimizer.restaurant_coords
    customer_coords = {oid: data['customer_coord'] for oid, data in orders_data.items()}
    driver_speed = optimizer.driver_speed
    travel_time_deviation_ratio = 0.2 # 保持与模块内一致
    theta_t = 0.3 # 保持与模块内一致
    travel_cost_per_unit = optimizer.travel_cost_per_unit
    penalty_cost_per_unit = optimizer.penalty_cost_per_unit
    current_stp_time = stp * optimizer.stp_interval
    stp_interval = optimizer.stp_interval
    estimated_delivery_duration = {oid: data['est_delivery'] for oid, data in orders_data.items()}

    nodes_in_sequence = ['start']
    arcs, delivery_arc_indices = [], {}
    cur = start_pos
    for i, node in enumerate(full_sequence):
        if node.startswith("pickup_"):
            oid = int(node.split("_")[1])
            nxt = restaurant_coords[orders_data[oid]['restaurant_id']]
            nodes_in_sequence.append(node)
        elif node.startswith("delivery_"):
            oid = int(node.split("_")[1])
            nxt = customer_coords[oid]
            delivery_arc_indices.setdefault(i, []).append(oid)
            nodes_in_sequence.append(node)
        else: continue
        arcs.append((cur, nxt))
        cur = nxt

    if not arcs: return 0.0, 0.0, 0.0

    num_arcs = len(arcs)
    nominal_times = [math.hypot(a[0] - b[0], a[1] - b[1]) / driver_speed for a, b in arcs]
    deviations = [t * travel_time_deviation_ratio for t in nominal_times]
    global_gamma = math.ceil(theta_t * num_arcs)

    # 内部辅助函数：在给定的(子)路径上运行DP，返回最坏情况的结束时间
    def _get_worst_arrival_time(path_arcs, path_nom_times, path_devs, sub_start_time, budget, path_node_types):
        n = len(path_arcs)
        if n == 0: return sub_start_time
        
        effective_budget = min(budget, n)
        
        dp = [[-1.0] * (effective_budget + 1) for _ in range(n + 1)]
        dp[0][0] = sub_start_time

        for i in range(n):
            t_nom, dev = path_nom_times[i], path_devs[i]

            node_type = path_node_types[i]
            food_ready_time = -1.0
            if node_type.startswith("pickup_"):
                oid = int(node_type.split("_")[1])
                order_stp = orders_data[oid]['stp']
                prep_time = orders_data[oid]['preparation_time']
                food_ready_time = order_stp * stp_interval + prep_time

            for g in range(effective_budget + 1):
                if dp[i][g] == -1.0:
                    continue

                current_departure = max(dp[i][g], food_ready_time) if food_ready_time != -1.0 else dp[i][g]
                
                # No dev
                next_arrival_no_dev = current_departure + t_nom
                if dp[i+1][g] == -1.0 or next_arrival_no_dev > dp[i+1][g]:
                    dp[i+1][g] = next_arrival_no_dev
                
                # With dev
                if g < effective_budget:
                    next_arrival_with_dev = current_departure + t_nom + dev
                    if dp[i+1][g+1] == -1.0 or next_arrival_with_dev > dp[i+1][g+1]:
                        dp[i+1][g+1] = next_arrival_with_dev

        worst_time = -1.0
        if dp[n]:
           try:
               worst_time = max(val for val in dp[n] if val != -1.0)
           except ValueError: # 如果所有值都是-1
               worst_time = -1.0
        return worst_time if worst_time != -1.0 else sub_start_time

    # 2. 精确计算旅行成本 (基于整条路径的DP)
    worst_finish_time = _get_worst_arrival_time(arcs, nominal_times, deviations, start_time, global_gamma, nodes_in_sequence)
    exact_travel_cost = (worst_finish_time - start_time) * travel_cost_per_unit

    # 3. 精确计算延迟成本 (对每个交付点，使用全局Gamma在其子路径上运行DP)
    exact_penalty_cost = 0.0
    for i in range(num_arcs):
        if i in delivery_arc_indices:
            sub_path_arcs = arcs[:i+1]
            sub_path_nom_times = nominal_times[:i+1]
            sub_path_devs = deviations[:i+1]
            sub_path_node_types = nodes_in_sequence[:i+2]
            
            worst_arrival_at_i = _get_worst_arrival_time(sub_path_arcs, sub_path_nom_times, sub_path_devs, start_time, global_gamma, sub_path_node_types)
            
            for oid in delivery_arc_indices[i]:
                target = current_stp_time + estimated_delivery_duration[oid]
                delay = max(0, worst_arrival_at_i - target)
                exact_penalty_cost += delay * penalty_cost_per_unit
    
    exact_total_cost = exact_travel_cost + exact_penalty_cost
    return exact_travel_cost, exact_penalty_cost, exact_total_cost

def test_complete_robust_socda():
    """完整测试Robust SOCDA扩展案例"""
    print("="*80)
    print("Robust SOCDA扩展案例测试")
    print("="*80)
    
    class MockRobustFoodDeliveryOptimizer:
        def __init__(self):
            # --- 与 Robust Gurobi 8.py 同步的参数 ---
            self.commission_rate = 0.18
            self.travel_cost_per_unit = 0.2
            self.penalty_cost_per_unit = 1.0
            self.driver_speed = 1.0
            self.max_orders_per_driver = 5
            self.driver_capacity = 30
            self.num_drivers = 2
            self.stps = list(range(6))
            self.stp_interval = 10
            self.slot_duration = 30

            # 客户选择与动态定价参数
            self.k_dist = 0.1
            self.k_val = 0.01
            self.num_customers = 18

            # 客户类型分配
            self.customer_types = {
                0: list(range(0, 3)),   # 价格至上型
                1: list(range(3, 6)),   # 时间敏感型 (早期)
                2: list(range(6, 9)),   # 时间敏感型 (中期)
                3: list(range(9, 12)),  # 时间敏感型 (晚期)
                4: list(range(12, 15)), # 务实平衡型
                5: list(range(15, 18))  # 品质无忧型
            }
            
            # 客户偏好列表 (rank越小越好)
            self.customer_preferences = {
                # Type 0: 价格至上
                0: {(0, 3): 1, (1, 3): 2, (2, 3): 3, (3, 3): 4, (4, 3): 5, (5, 3): 6, 'no_purchase': 7,
                    (0, 6): 8, (1, 6): 9, (2, 6): 10, (3, 6): 11, (4, 6): 12, (5, 6): 13},
                # Type 1: 时间敏感 (早期)
                1: {(0, 3): 1, (0, 6): 2, (1, 3): 3, (1, 6): 4, 'no_purchase': 5,
                    (2, 3): 6, (2, 6): 7, (3, 3): 8, (3, 6): 9, (4, 3): 10, (4, 6): 11, (5, 3): 12, (5, 6): 13},
                # Type 2: 时间敏感 (中期)
                2: {(2, 3): 1, (2, 6): 2, (3, 3): 3, (3, 6): 4, 'no_purchase': 5,
                    (0, 3): 6, (0, 6): 7, (1, 3): 8, (1, 6): 9, (4, 3): 10, (4, 6): 11, (5, 3): 12, (5, 6): 13},
                # Type 3: 时间敏感 (晚期)
                3: {(4, 3): 1, (4, 6): 2, (5, 3): 3, (5, 6): 4, 'no_purchase': 5,
                    (0, 3): 6, (0, 6): 7, (1, 3): 8, (1, 6): 9, (2, 3): 10, (2, 6): 11, (3, 3): 12, (3, 6): 13},
                # Type 4: 务实平衡型
                4: {(0, 3): 1, (1, 3): 2, (0, 6): 3, (2, 3): 4, 'no_purchase': 5,
                    (1, 6): 6, (3, 3): 7, (2, 6): 8, (4, 3): 9, (3, 6): 10, (5, 3): 11, (4, 6): 12, (5, 6): 13},
                # Type 5: 品质无忧型
                5: {(2, 6): 1, (3, 6): 2, (2, 3): 3, (3, 3): 4, (1, 6): 5, (4, 6): 6, (1, 3): 7, (4, 3): 8,
                    (0, 6): 9, (5, 6): 10, (0, 3): 11, (5, 3): 12, 'no_purchase': 13}
            }

            self.restaurant_coords = {
                0: (2, 2),
                1: (8, 2)
            }
            self.driver_start_coords = {
                0: (5, 1),
                1: (6, 1)
            }
            
            # 统一使用 setup_orders_data 进行初始化
            self.setup_orders_data()

        def calculate_distance(self, coord1, coord2):
            return math.sqrt((coord1[0] - coord2[0])**2 + (coord1[1] - coord2[1])**2)

        def setup_orders_data(self):
            """设置所有潜在订单的硬编码数据"""
            self.potential_orders_data = {
                0: {'value': 25, 'restaurant_id': 0, 'customer_coord': (1, 5), 'est_delivery': 15, 'volume': 5, 'preparation_time': 5.0},
                1: {'value': 30, 'restaurant_id': 1, 'customer_coord': (9, 4), 'est_delivery': 12, 'volume': 6, 'preparation_time': 6.0},
                2: {'value': 28, 'restaurant_id': 0, 'customer_coord': (3, 6), 'est_delivery': 18, 'volume': 6, 'preparation_time': 5.6},
                3: {'value': 32, 'restaurant_id': 1, 'customer_coord': (7, 5), 'est_delivery': 14, 'volume': 7, 'preparation_time': 6.4},
                4: {'value': 26, 'restaurant_id': 0, 'customer_coord': (2, 7), 'est_delivery': 16, 'volume': 6, 'preparation_time': 5.2},
                5: {'value': 35, 'restaurant_id': 1, 'customer_coord': (8, 6), 'est_delivery': 13, 'volume': 7, 'preparation_time': 7.0},
                6: {'value': 29, 'restaurant_id': 0, 'customer_coord': (4, 5), 'est_delivery': 15, 'volume': 6, 'preparation_time': 5.8},
                7: {'value': 33, 'restaurant_id': 1, 'customer_coord': (6, 7), 'est_delivery': 17, 'volume': 7, 'preparation_time': 6.6},
                8: {'value': 27, 'restaurant_id': 0, 'customer_coord': (1, 8), 'est_delivery': 20, 'volume': 6, 'preparation_time': 5.4},
                9: {'value': 31, 'restaurant_id': 1, 'customer_coord': (9, 7), 'est_delivery': 16, 'volume': 7, 'preparation_time': 6.2},
                10: {'value': 24, 'restaurant_id': 0, 'customer_coord': (3, 4), 'est_delivery': 14, 'volume': 5, 'preparation_time': 4.8},
                11: {'value': 36, 'restaurant_id': 1, 'customer_coord': (7, 8), 'est_delivery': 18, 'volume': 8, 'preparation_time': 7.2},
                12: {'value': 28, 'restaurant_id': 0, 'customer_coord': (2, 6), 'est_delivery': 15, 'volume': 6, 'preparation_time': 5.6},
                13: {'value': 34, 'restaurant_id': 1, 'customer_coord': (8, 5), 'est_delivery': 13, 'volume': 7, 'preparation_time': 6.8},
                14: {'value': 30, 'restaurant_id': 0, 'customer_coord': (4, 7), 'est_delivery': 17, 'volume': 6, 'preparation_time': 6.0},
                15: {'value': 32, 'restaurant_id': 1, 'customer_coord': (6, 8), 'est_delivery': 16, 'volume': 7, 'preparation_time': 6.4},
                16: {'value': 26, 'restaurant_id': 0, 'customer_coord': (1, 6), 'est_delivery': 18, 'volume': 6, 'preparation_time': 5.2},
                17: {'value': 37, 'restaurant_id': 1, 'customer_coord': (9, 6), 'est_delivery': 14, 'volume': 8, 'preparation_time': 7.4}
            }
            # 保持 orders_data 属性以兼容现有代码
            self.orders_data = self.potential_orders_data
            
            # 预计算配送距离
            self.delivery_distances = {}
            for i in range(self.num_customers):
                order_info = self.potential_orders_data[i]
                rest_id = order_info['restaurant_id']
                rest_coord = self.restaurant_coords[rest_id]
                cust_coord = order_info['customer_coord']
                self.delivery_distances[i] = self.calculate_distance(rest_coord, cust_coord)

    mock_optimizer = MockRobustFoodDeliveryOptimizer()
    # 确保将完整的订单数据传递给算法
    robust_socda = RobustSOCDA(mock_optimizer)
        
    pricing_schemes = [(3, 3), (3, 6), (6, 3), (6, 6)]
    all_results = []
        
    for price_slot1, price_slot2 in pricing_schemes:
        print(f"\n{'='*70}\n测试鲁棒定价方案: (时段1={price_slot1}, 时段2={price_slot2})\n{'='*70}")
        
        # 1. 客户选择与收入计算
        customer_choices, orders_by_stp = get_all_customer_choices(
            (price_slot1, price_slot2), mock_optimizer.num_customers,
            mock_optimizer.customer_types, mock_optimizer.customer_preferences,
            mock_optimizer.stp_interval, mock_optimizer.potential_orders_data,
            mock_optimizer.delivery_distances, mock_optimizer.k_dist, mock_optimizer.k_val
        )
        
        total_order_value = 0
        total_delivery_fee = 0
        print("客户选择结果与动态费用:")
        for cid, choice in enumerate(customer_choices):
            if choice:
                _, base_price = choice
                order_value = mock_optimizer.potential_orders_data[cid]['value']
                dynamic_fee = _calculate_delivery_fee(
                    cid, base_price, mock_optimizer.potential_orders_data,
                    mock_optimizer.delivery_distances, mock_optimizer.k_dist, mock_optimizer.k_val
                )
                total_order_value += order_value
                total_delivery_fee += dynamic_fee
                print(f"  客户 {cid}: 选择STP {choice[0]}, 基础价 {base_price}, 实际支付 ¥{dynamic_fee:.2f}")
            else:
                print(f"  客户 {cid}: 选择不购买")

        commission = total_order_value * mock_optimizer.commission_rate
        revenue = commission + total_delivery_fee
        print(f"\n总收入: ¥{revenue:.2f} (佣金 ¥{commission:.2f}, 配送费 ¥{total_delivery_fee:.2f})")

        # 2. 逐STP进行路径规划
        driver_states = {
            k: {'pos': mock_optimizer.driver_start_coords[k], 'time': 0.0}
            for k in range(mock_optimizer.num_drivers)
        }
        
        total_travel = 0.0
        total_penalty = 0.0

        for stp in mock_optimizer.stps:
            stp_orders = orders_by_stp[stp]
            
            # 更新司机时间
            for k in driver_states:
                 driver_states[k]['time'] = max(driver_states[k]['time'], stp * mock_optimizer.stp_interval)

            travel, penalty, final_drivers, _ = robust_socda.solve_single_stp_robust_dispatch(
                stp_orders, stp, driver_states)
            
            total_travel += travel
            total_penalty += penalty
            
            # 更新司机状态
            for driver in final_drivers:
                driver_states[driver.driver_id] = {'pos': driver.current_pos, 'time': driver.current_time}
        
        # 3. 计算利润
        total_cost = total_travel + total_penalty
        profit = revenue - total_cost
            
        result = {
            'scheme': (price_slot1, price_slot2), 'revenue': revenue,
            'robust_travel_cost': total_travel, 'robust_penalty_cost': total_penalty,
            'total_robust_cost': total_cost, 'robust_profit': profit,
            'initial_driver_states_for_recalc': { k: {'pos': mock_optimizer.driver_start_coords[k], 'time': 0.0} for k in range(mock_optimizer.num_drivers)}
        }
        all_results.append(result)
            
        print(f"\n--- 方案({price_slot1}, {price_slot2})汇总 ---")
        print(f"总收入: {revenue:.2f}, 总鲁棒成本: {total_cost:.2f}, 总鲁棒利润: {profit:.2f}")
        
    print(f"\n{'='*80}\n所有鲁棒定价方案结果汇总对比\n{'='*80}")
    print(f"{'方案':<12} {'收入':<10} {'旅行成本':<12} {'延迟成本':<12} {'总成本':<12} {'利润':<10} {'最优':<6}")
    print("-" * 85)
    best_res = max(all_results, key=lambda x: x['robust_profit'])
    for res in all_results:
        scheme_str = f"({res['scheme'][0]}, {res['scheme'][1]})"
        is_best = "★" if res == best_res else ""
        print(f"{scheme_str:<12} {res['revenue']:<10.2f} {res['robust_travel_cost']:<12.2f} "
              f"{res['robust_penalty_cost']:<12.2f} {res['total_robust_cost']:<12.2f} "
              f"{res['robust_profit']:<10.2f} {is_best:<6}")
        
    # --- (新增) 后处理：使用精确DP重新计算所有方案的成本 ---
    print(f"\n\n{'='*80}")
    print("后处理：使用精确DP逻辑重新计算最终成本")
    print(f"{'='*80}")
    
    recalculated_results = []
    for result in all_results:
        print(f"\n--- 重新计算方案: {result['scheme']} ---")
        
        # 重新获取该方案下的客户选择和订单
        price_slot1, price_slot2 = result['scheme']
        _, orders_by_stp = get_all_customer_choices(
            (price_slot1, price_slot2), mock_optimizer.num_customers,
            mock_optimizer.customer_types, mock_optimizer.customer_preferences,
            mock_optimizer.stp_interval, mock_optimizer.potential_orders_data,
            mock_optimizer.delivery_distances, mock_optimizer.k_dist, mock_optimizer.k_val
        )
        
        # (FIX) 为当前重算场景准备带有 'stp' 信息的订单数据
        # 这是修复 KeyError: 'stp' 的关键
        current_run_orders_data = copy.deepcopy(mock_optimizer.potential_orders_data)
        order_to_stp_map = {oid: stp for stp, oids in orders_by_stp.items() for oid in oids}
        for oid, stp_val in order_to_stp_map.items():
            if oid in current_run_orders_data:
                current_run_orders_data[oid]['stp'] = stp_val
        
        # 通过在位更新，确保所有持有 orders_data 引用的模块（如OGGM, OGAH）都能看到本次运行的正确数据
        mock_optimizer.orders_data.clear()
        mock_optimizer.orders_data.update(current_run_orders_data)
        
        driver_states = copy.deepcopy(result['initial_driver_states_for_recalc'])
        
        exact_total_travel = 0
        exact_total_penalty = 0

        for stp in mock_optimizer.stps:
            stp_orders = orders_by_stp[stp]
            
            # 更新司机时间
            for k in driver_states:
                driver_states[k]['time'] = max(driver_states[k]['time'], stp * mock_optimizer.stp_interval)

            # 必须调用solver来获取路径分配
            _, _, final_drivers, _ = robust_socda.solve_single_stp_robust_dispatch(
                stp_orders, stp, driver_states
            )
            
            # 对每个有任务的司机，获取其完整路径并用精确DP重算
            for driver in final_drivers:
                if not driver.sequence: continue
                
                # 注意：此处使用的 start_pos 和 start_time 是司机在整个仿真历史中的初始状态，
                # 而不是每个STP开始时的状态，因为 compute_exact_robust_cost_for_route 评估的是从一开始的整条路径。
                # 这与OGAH中边际成本的计算方式不同，后者是基于司机当前状态的。
                # 为了与RS7对齐，我们坚持使用司机的 absolute initial state.
                initial_driver_state = result['initial_driver_states_for_recalc'][driver.driver_id]
                
                # 使用精确DP函数计算
                exact_travel, exact_penalty, _ = compute_exact_robust_cost_for_route(
                    driver.sequence, driver.initial_pos, driver.initial_time, stp, mock_optimizer
                )
                
                exact_total_travel += exact_travel
                exact_total_penalty += exact_penalty

            # 更新司机状态以继续模拟
            for driver in final_drivers:
                driver_states[driver.driver_id] = {'pos': driver.current_pos, 'time': driver.current_time}
        
        exact_total_cost = exact_total_travel + exact_total_penalty
        exact_profit = result['revenue'] - exact_total_cost

        recalculated_results.append({
            'scheme': result['scheme'],
            'revenue': result['revenue'],
            'travel_cost': exact_total_travel,
            'penalty_cost': exact_total_penalty,
            'total_cost': exact_total_cost,
            'profit': exact_profit
        })

    # --- (新增) 打印精确重算后的结果 ---
    print(f"\n\n{'='*80}")
    print("精确重算后的鲁棒成本与利润对比")
    print(f"{'='*80}")
    print(f"{'方案':<12} {'收入':<10} {'精确旅行':<12} {'精确延迟':<12} {'精确总成本':<14} {'精确利润':<12} {'最优':<6}")
    print("-" * 95)
    
    if recalculated_results:
        best_recalc_scheme = max(recalculated_results, key=lambda x: x['profit'])
        for result in recalculated_results:
            scheme_str = f"({result['scheme'][0]}, {result['scheme'][1]})"
            is_best = "★" if result == best_recalc_scheme else ""
            print(f"{scheme_str:<12} {result['revenue']:<10.2f} {result['travel_cost']:<12.2f} "
                  f"{result['penalty_cost']:<12.2f} {result['total_cost']:<14.2f} "
                  f"{result['profit']:<12.2f} {is_best:<6}")

    return robust_socda, all_results

def main():
    test_complete_robust_socda()

if __name__ == "__main__":
    main()